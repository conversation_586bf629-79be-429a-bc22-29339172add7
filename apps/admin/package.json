{"name": "@azkuja/admin", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 5050", "build": "next build", "start": "next start -p 8080", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "echo \"No tests configured for admin\"", "clean": "rm -rf .next dist"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@mui/icons-material": "^6.2.1", "@mui/lab": "^6.0.0-beta.19", "@mui/material": "^6.2.1", "@mui/system": "^6.2.1", "@mui/x-data-grid": "^7.29.8", "@mui/x-date-pickers": "^7.29.4", "@refinedev/core": "^4.57.10", "@refinedev/inferencer": "^5.1.1", "@refinedev/kbar": "^1.3.16", "@refinedev/mui": "^6.2.2", "@refinedev/nextjs-router": "^6.2.3", "@refinedev/react-hook-form": "^4.10.2", "@refinedev/react-table": "^5.6.17", "@tanstack/react-query": "^5.83.0", "@tanstack/react-table": "^8.21.3", "@types/lodash-es": "^4.17.12", "axios": "^1.11.0", "date-fns": "^2.30.0", "dayjs": "^1.11.13", "lodash-es": "^4.17.21", "next": "^15.4.4", "nookies": "^2.5.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.61.1", "recharts": "^3.1.0"}, "devDependencies": {"@types/lodash-es": "^4.17.12", "@types/node": "^24.1.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "autoprefixer": "^10.4.21", "eslint": "^9.32.0", "eslint-config-next": "^15.4.4", "postcss": "^8.5.6", "tailwindcss": "^3.4.0", "typescript": "^5.8.3"}}