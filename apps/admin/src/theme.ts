import { createTheme, Theme } from '@mui/material/styles';

// Define custom types for extended palette
declare module '@mui/material/styles' {
  interface Palette {
    customColors: {
      bodyBg: string;
      chatBg: string;
      greyLightBg: string;
      inputBorder: string;
      tableHeaderBg: string;
      tooltipText: string;
      trackBg: string;
    };
  }

  interface PaletteOptions {
    customColors?: {
      bodyBg?: string;
      chatBg?: string;
      greyLightBg?: string;
      inputBorder?: string;
      tableHeaderBg?: string;
      tooltipText?: string;
      trackBg?: string;
    };
  }
}

// Color schemes based on Materialize design
const lightColorScheme = {
  primary: {
    main: '#666CFF',
    light: '#8589FF',
    dark: '#5C61E6',
    contrastText: '#FFFFFF',
  },
  secondary: {
    main: '#6D788D',
    light: '#8A93A4',
    dark: '#626C7F',
    contrastText: '#FFFFFF',
  },
  error: {
    main: '#FF4D49',
    light: '#FF716D',
    dark: '#E64542',
    contrastText: '#FFFFFF',
  },
  warning: {
    main: '#FDB528',
    light: '#FDC453',
    dark: '#E4A324',
    contrastText: '#FFFFFF',
  },
  info: {
    main: '#26C6F9',
    light: '#51D1FA',
    dark: '#22B3E1',
    contrastText: '#FFFFFF',
  },
  success: {
    main: '#72E128',
    light: '#8EE753',
    dark: '#67CB24',
    contrastText: '#FFFFFF',
  },
  background: {
    default: '#F7F7F9',
    paper: '#FFFFFF',
  },
  text: {
    primary: 'rgba(58, 53, 65, 0.9)',
    secondary: 'rgba(58, 53, 65, 0.7)',
    disabled: 'rgba(58, 53, 65, 0.4)',
  },
  divider: 'rgba(58, 53, 65, 0.12)',
  action: {
    active: 'rgba(58, 53, 65, 0.6)',
    hover: 'rgba(58, 53, 65, 0.06)',
    selected: 'rgba(58, 53, 65, 0.08)',
    disabled: 'rgba(58, 53, 65, 0.3)',
    disabledBackground: 'rgba(58, 53, 65, 0.16)',
  },
  customColors: {
    bodyBg: '#F7F7F9',
    chatBg: '#F7F6FA',
    greyLightBg: '#FAFAFA',
    inputBorder: 'rgba(58, 53, 65, 0.22)',
    tableHeaderBg: '#F5F5F7',
    tooltipText: '#FFFFFF',
    trackBg: '#F5F5F8',
  },
};

const darkColorScheme = {
  primary: {
    main: '#666CFF',
    light: '#8589FF',
    dark: '#5C61E6',
    contrastText: '#FFFFFF',
  },
  secondary: {
    main: '#6D788D',
    light: '#8A93A4',
    dark: '#626C7F',
    contrastText: '#FFFFFF',
  },
  error: {
    main: '#FF4D49',
    light: '#FF716D',
    dark: '#E64542',
    contrastText: '#FFFFFF',
  },
  warning: {
    main: '#FDB528',
    light: '#FDC453',
    dark: '#E4A324',
    contrastText: '#FFFFFF',
  },
  info: {
    main: '#26C6F9',
    light: '#51D1FA',
    dark: '#22B3E1',
    contrastText: '#FFFFFF',
  },
  success: {
    main: '#72E128',
    light: '#8EE753',
    dark: '#67CB24',
    contrastText: '#FFFFFF',
  },
  background: {
    default: '#282A42', // Dark background
    paper: '#30334E', // Dark paper
  },
  text: {
    primary: 'rgba(234, 234, 255, 0.9)',
    secondary: 'rgba(234, 234, 255, 0.7)',
    disabled: 'rgba(234, 234, 255, 0.4)',
  },
  divider: 'rgba(234, 234, 255, 0.12)',
  action: {
    active: 'rgba(234, 234, 255, 0.6)',
    hover: 'rgba(234, 234, 255, 0.06)',
    selected: 'rgba(234, 234, 255, 0.08)',
    disabled: 'rgba(234, 234, 255, 0.3)',
    disabledBackground: 'rgba(234, 234, 255, 0.16)',
  },
  customColors: {
    bodyBg: '#282A42',
    chatBg: '#343752',
    greyLightBg: '#333851',
    inputBorder: 'rgba(234, 234, 255, 0.22)',
    tableHeaderBg: '#3A3E5B',
    tooltipText: '#30334E',
    trackBg: '#3C3F59',
  },
};

// Create theme function
export const createAppTheme = (mode: 'light' | 'dark') =>
  createTheme({
    direction: 'rtl',
    palette: {
      mode,
      ...(mode === 'light' ? lightColorScheme : darkColorScheme),
    },

    typography: {
      fontFamily: [
        'Vazirmatn',
        'Inter',
        '-apple-system',
        'BlinkMacSystemFont',
        '"Segoe UI"',
        'Roboto',
        '"Helvetica Neue"',
        'Arial',
        'sans-serif',
      ].join(','),
      h1: {
        fontSize: '2.5rem',
        fontWeight: 700,
        lineHeight: 1.2,
      },
      h2: {
        fontSize: '2rem',
        fontWeight: 600,
        lineHeight: 1.3,
      },
      h3: {
        fontSize: '1.75rem',
        fontWeight: 600,
        lineHeight: 1.3,
      },
      h4: {
        fontSize: '1.5rem',
        fontWeight: 600,
        lineHeight: 1.4,
      },
      h5: {
        fontSize: '1.25rem',
        fontWeight: 600,
        lineHeight: 1.4,
      },
      h6: {
        fontSize: '1.125rem',
        fontWeight: 600,
        lineHeight: 1.4,
      },
      body1: {
        fontSize: '1rem',
        lineHeight: 1.6,
      },
      body2: {
        fontSize: '0.875rem',
        lineHeight: 1.6,
      },
      button: {
        textTransform: 'none',
        fontWeight: 500,
      },
    },

    shape: {
      borderRadius: 10,
    },

    spacing: 8,

    shadows: [
      'none',
      '0px 2px 1px -1px rgba(58, 53, 65, 0.06), 0px 1px 1px 0px rgba(58, 53, 65, 0.04), 0px 1px 3px 0px rgba(58, 53, 65, 0.08)',
      '0px 3px 1px -2px rgba(58, 53, 65, 0.06), 0px 2px 2px 0px rgba(58, 53, 65, 0.04), 0px 1px 5px 0px rgba(58, 53, 65, 0.08)',
      '0px 3px 3px -2px rgba(58, 53, 65, 0.06), 0px 3px 4px 0px rgba(58, 53, 65, 0.04), 0px 1px 8px 0px rgba(58, 53, 65, 0.08)',
      '0px 2px 4px -1px rgba(58, 53, 65, 0.06), 0px 4px 5px 0px rgba(58, 53, 65, 0.04), 0px 1px 10px 0px rgba(58, 53, 65, 0.08)',
      '0px 3px 5px -1px rgba(58, 53, 65, 0.06), 0px 5px 8px 0px rgba(58, 53, 65, 0.04), 0px 1px 14px 0px rgba(58, 53, 65, 0.08)',
      '0px 3px 5px -1px rgba(58, 53, 65, 0.06), 0px 6px 10px 0px rgba(58, 53, 65, 0.04), 0px 1px 18px 0px rgba(58, 53, 65, 0.08)',
      '0px 4px 5px -2px rgba(58, 53, 65, 0.06), 0px 7px 10px 1px rgba(58, 53, 65, 0.04), 0px 2px 16px 1px rgba(58, 53, 65, 0.08)',
      '0px 5px 5px -3px rgba(58, 53, 65, 0.06), 0px 8px 10px 1px rgba(58, 53, 65, 0.04), 0px 3px 14px 2px rgba(58, 53, 65, 0.08)',
      '0px 5px 6px -3px rgba(58, 53, 65, 0.06), 0px 9px 12px 1px rgba(58, 53, 65, 0.04), 0px 3px 16px 2px rgba(58, 53, 65, 0.08)',
      '0px 6px 6px -3px rgba(58, 53, 65, 0.06), 0px 10px 14px 1px rgba(58, 53, 65, 0.04), 0px 4px 18px 3px rgba(58, 53, 65, 0.08)',
      '0px 6px 7px -4px rgba(58, 53, 65, 0.06), 0px 11px 15px 1px rgba(58, 53, 65, 0.04), 0px 4px 20px 3px rgba(58, 53, 65, 0.08)',
      '0px 7px 8px -4px rgba(58, 53, 65, 0.06), 0px 12px 17px 2px rgba(58, 53, 65, 0.04), 0px 5px 22px 4px rgba(58, 53, 65, 0.08)',
      '0px 7px 8px -4px rgba(58, 53, 65, 0.06), 0px 13px 19px 2px rgba(58, 53, 65, 0.04), 0px 5px 24px 4px rgba(58, 53, 65, 0.08)',
      '0px 7px 9px -4px rgba(58, 53, 65, 0.06), 0px 14px 21px 2px rgba(58, 53, 65, 0.04), 0px 5px 26px 4px rgba(58, 53, 65, 0.08)',
      '0px 8px 9px -5px rgba(58, 53, 65, 0.06), 0px 15px 22px 2px rgba(58, 53, 65, 0.04), 0px 6px 28px 5px rgba(58, 53, 65, 0.08)',
      '0px 8px 10px -5px rgba(58, 53, 65, 0.06), 0px 16px 24px 2px rgba(58, 53, 65, 0.04), 0px 6px 30px 5px rgba(58, 53, 65, 0.08)',
      '0px 8px 11px -5px rgba(58, 53, 65, 0.06), 0px 17px 26px 2px rgba(58, 53, 65, 0.04), 0px 6px 32px 5px rgba(58, 53, 65, 0.08)',
      '0px 9px 11px -5px rgba(58, 53, 65, 0.06), 0px 18px 28px 2px rgba(58, 53, 65, 0.04), 0px 7px 34px 6px rgba(58, 53, 65, 0.08)',
      '0px 9px 12px -6px rgba(58, 53, 65, 0.06), 0px 19px 29px 2px rgba(58, 53, 65, 0.04), 0px 7px 36px 6px rgba(58, 53, 65, 0.08)',
      '0px 10px 13px -6px rgba(58, 53, 65, 0.06), 0px 20px 31px 3px rgba(58, 53, 65, 0.04), 0px 8px 38px 7px rgba(58, 53, 65, 0.08)',
      '0px 10px 13px -6px rgba(58, 53, 65, 0.06), 0px 21px 33px 3px rgba(58, 53, 65, 0.04), 0px 8px 40px 7px rgba(58, 53, 65, 0.08)',
      '0px 10px 14px -6px rgba(58, 53, 65, 0.06), 0px 22px 35px 3px rgba(58, 53, 65, 0.04), 0px 8px 42px 7px rgba(58, 53, 65, 0.08)',
      '0px 11px 14px -7px rgba(58, 53, 65, 0.06), 0px 23px 36px 3px rgba(58, 53, 65, 0.04), 0px 9px 44px 8px rgba(58, 53, 65, 0.08)',
      '0px 11px 15px -7px rgba(58, 53, 65, 0.06), 0px 24px 38px 3px rgba(58, 53, 65, 0.04), 0px 9px 46px 8px rgba(58, 53, 65, 0.08)',
    ],

    components: {
      MuiCssBaseline: {
        styleOverrides: {
          body: {
            backgroundColor: mode === 'light' ? '#F7F7F9' : '#282A42',
            color:
              mode === 'light'
                ? 'rgba(58, 53, 65, 0.87)'
                : 'rgba(234, 234, 255, 0.87)',
          },
        },
      },

      MuiCard: {
        styleOverrides: {
          root: {
            borderRadius: '10px !important', // Force 10px override inline styles
            boxShadow:
              mode === 'light'
                ? '0px 2px 6px rgba(58, 53, 65, 0.1)'
                : '0px 2px 6px rgba(0, 0, 0, 0.3)',
            border:
              mode === 'light'
                ? '1px solid rgba(58, 53, 65, 0.12)'
                : '1px solid rgba(234, 234, 255, 0.12)',
            backgroundColor: mode === 'light' ? '#FFFFFF' : '#30334E',
            transition: 'all 0.3s ease',
            '&:hover': {
              boxShadow:
                mode === 'light'
                  ? '0px 4px 12px rgba(58, 53, 65, 0.15)'
                  : '0px 4px 12px rgba(0, 0, 0, 0.4)',
            },
          },
        },
      },

      MuiButton: {
        styleOverrides: {
          root: {
            borderRadius: 8,
            fontWeight: 500,
            padding: '10px 20px',
            textTransform: 'none',
            transition: 'all 0.3s ease',
          },
          contained: {
            boxShadow: 'none',
            '&:hover': {
              boxShadow: '0 4px 8px rgba(102, 108, 255, 0.3)',
              transform: 'translateY(-2px)',
            },
          },
        },
      },

      MuiPaper: {
        styleOverrides: {
          root: {
            borderRadius: '10px !important', // Force 10px override inline styles
            backgroundColor: mode === 'light' ? '#FFFFFF' : '#30334E',
            border:
              mode === 'light'
                ? '1px solid rgba(58, 53, 65, 0.12)'
                : '1px solid rgba(234, 234, 255, 0.12)',
          },
        },
      },

      MuiTextField: {
        styleOverrides: {
          root: {
            '& .MuiOutlinedInput-root': {
              borderRadius: 8,
              backgroundColor: mode === 'light' ? '#FFFFFF' : '#30334E',
              '& fieldset': {
                borderColor:
                  mode === 'light'
                    ? 'rgba(58, 53, 65, 0.22)'
                    : 'rgba(234, 234, 255, 0.22)',
              },
              '&:hover fieldset': {
                borderColor:
                  mode === 'light'
                    ? 'rgba(58, 53, 65, 0.32)'
                    : 'rgba(234, 234, 255, 0.32)',
              },
              '&.Mui-focused fieldset': {
                borderColor: '#666CFF',
              },
            },
          },
        },
      },

      MuiAppBar: {
        styleOverrides: {
          root: {
            backgroundColor: mode === 'light' ? '#FFFFFF' : '#30334E',
            color:
              mode === 'light'
                ? 'rgba(58, 53, 65, 0.87)'
                : 'rgba(234, 234, 255, 0.87)',
            boxShadow:
              mode === 'light'
                ? '0 2px 6px rgba(58, 53, 65, 0.12)'
                : '0 2px 6px rgba(0, 0, 0, 0.3)',
            borderBottom:
              mode === 'light'
                ? '1px solid rgba(58, 53, 65, 0.12)'
                : '1px solid rgba(234, 234, 255, 0.12)',
            borderRadius: '0px !important',
            borderTopLeftRadius: '0px !important',
            borderTopRightRadius: '0px !important',
            borderBottomLeftRadius: '0px !important',
            borderBottomRightRadius: '0px !important',
          },
        },
      },

      MuiDrawer: {
        styleOverrides: {
          paper: {
            backgroundColor: '#30334E', // Always semi-dark for sidebar
            color: '#EAEAFF',
            borderRight: '1px solid rgba(234, 234, 255, 0.12)',
            boxShadow: 'none',
            borderRadius: '0px !important',
            borderTopLeftRadius: '0px !important',
            borderTopRightRadius: '0px !important',
            borderBottomLeftRadius: '0px !important',
            borderBottomRightRadius: '0px !important',
          },
        },
      },

      MuiListItem: {
        styleOverrides: {
          root: {
            borderRadius: 8,
            margin: '4px 8px',
            color: 'rgba(234, 234, 255, 0.87)',
            transition: 'all 0.3s ease',
            '&.Mui-selected': {
              backgroundColor: '#666CFF',
              color: '#FFFFFF',
              '&:hover': {
                backgroundColor: '#5C61E6',
              },
              '& .MuiListItemIcon-root': {
                color: '#FFFFFF',
              },
            },
            '&:hover': {
              backgroundColor: 'rgba(234, 234, 255, 0.08)',
              borderRadius: 8,
              transform: 'translateX(4px)', // RTL animation
            },
          },
        },
      },

      MuiListItemIcon: {
        styleOverrides: {
          root: {
            color: 'rgba(234, 234, 255, 0.68)',
            minWidth: 44,
          },
        },
      },

      MuiListItemText: {
        styleOverrides: {
          primary: {
            color: 'rgba(234, 234, 255, 0.87)',
            fontWeight: 500,
          },
        },
      },

      MuiChip: {
        styleOverrides: {
          root: {
            borderRadius: 8,
            fontWeight: 500,
          },
        },
      },

      MuiIconButton: {
        styleOverrides: {
          root: {
            borderRadius: 8,
            transition: 'all 0.3s ease',
            '&:hover': {
              backgroundColor:
                mode === 'light'
                  ? 'rgba(58, 53, 65, 0.08)'
                  : 'rgba(234, 234, 255, 0.08)',
            },
          },
        },
      },
    },
  });

// Default export with light theme
export const theme = createAppTheme('light');
