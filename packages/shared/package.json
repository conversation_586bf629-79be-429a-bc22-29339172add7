{"name": "@azkuja/shared", "version": "1.0.0", "private": true, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "echo \"No linting configured for shared package\"", "type-check": "tsc --noEmit", "test": "echo \"No tests configured for shared package\"", "clean": "rm -rf dist"}, "dependencies": {"react": "^18.0.0"}, "devDependencies": {"@types/ioredis": "^5.0.0", "@types/react": "^18.0.0", "typescript": "^5.2.2"}, "peerDependencies": {"react": "^18.0.0"}}