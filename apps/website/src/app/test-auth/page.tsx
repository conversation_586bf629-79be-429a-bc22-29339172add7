"use client";

import React, { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";

const TestAuthPage = () => {
  const { user, isLoading, isAuthenticated, login, verifyOtp, logout } =
    useAuth();
  const [phoneNumber, setPhoneNumber] = useState("");
  const [otpCode, setOtpCode] = useState("");
  const [step, setStep] = useState<"phone" | "otp">("phone");
  const [error, setError] = useState("");
  const [message, setMessage] = useState("");
  const [localStorageData, setLocalStorageData] = useState<{
    auth_token: string | null;
    auth_user: string | null;
  }>({ auth_token: null, auth_user: null });

  // Function to check localStorage data
  const checkLocalStorage = () => {
    if (typeof window !== "undefined") {
      setLocalStorageData({
        auth_token: localStorage.getItem("auth_token"),
        auth_user: localStorage.getItem("auth_user"),
      });
    }
  };

  // Check localStorage on component mount and when auth state changes
  useEffect(() => {
    checkLocalStorage();
  }, [user, isAuthenticated]);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setMessage("");

    try {
      const response = await login({ phone_number: phoneNumber });
      setMessage(response.message);
      setStep("otp");
    } catch (error) {
      setError(error instanceof Error ? error.message : "Login failed");
    }
  };

  const handleVerifyOtp = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");

    try {
      await verifyOtp({ phone_number: phoneNumber, otp_code: otpCode });
      setMessage("Login successful!");
      setStep("phone");
      setPhoneNumber("");
      setOtpCode("");
      // Check localStorage after successful login
      setTimeout(checkLocalStorage, 100);
    } catch (error) {
      setError(
        error instanceof Error ? error.message : "OTP verification failed"
      );
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
      setMessage("Logged out successfully");
      setStep("phone");
      setPhoneNumber("");
      setOtpCode("");
      // Check localStorage after logout
      setTimeout(checkLocalStorage, 100);
    } catch (error) {
      setError(error instanceof Error ? error.message : "Logout failed");
    }
  };

  const clearLocalStorage = () => {
    if (typeof window !== "undefined") {
      localStorage.removeItem("auth_token");
      localStorage.removeItem("auth_user");
      checkLocalStorage();
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">
            🧪 Authentication Persistence Test
          </h1>

          {/* Authentication Status */}
          <div className="mb-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h2 className="text-lg font-semibold text-blue-800 mb-2">
              Authentication Status
            </h2>
            <div className="space-y-2 text-sm">
              <div>
                <span className="font-medium">Loading:</span>
                <span
                  className={`ml-2 px-2 py-1 rounded ${isLoading ? "bg-yellow-100 text-yellow-800" : "bg-green-100 text-green-800"}`}
                >
                  {isLoading ? "Yes" : "No"}
                </span>
              </div>
              <div>
                <span className="font-medium">Authenticated:</span>
                <span
                  className={`ml-2 px-2 py-1 rounded ${isAuthenticated ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}`}
                >
                  {isAuthenticated ? "Yes" : "No"}
                </span>
              </div>
              <div>
                <span className="font-medium">User:</span>
                <span className="ml-2 text-gray-700">
                  {user ? `${user.name} (${user.phone_number})` : "None"}
                </span>
              </div>
            </div>
          </div>

          {/* localStorage Data */}
          <div className="mb-8 p-4 bg-gray-50 border border-gray-200 rounded-lg">
            <div className="flex justify-between items-center mb-2">
              <h2 className="text-lg font-semibold text-gray-800">
                localStorage Data
              </h2>
              <div className="space-x-2">
                <button
                  onClick={checkLocalStorage}
                  className="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600"
                >
                  Refresh
                </button>
                <button
                  onClick={clearLocalStorage}
                  className="px-3 py-1 bg-red-500 text-white rounded text-sm hover:bg-red-600"
                >
                  Clear
                </button>
              </div>
            </div>
            <div className="space-y-2 text-sm font-mono">
              <div>
                <span className="font-medium">auth_token:</span>
                <span className="ml-2 text-gray-700 break-all">
                  {localStorageData.auth_token || "null"}
                </span>
              </div>
              <div>
                <span className="font-medium">auth_user:</span>
                <span className="ml-2 text-gray-700 break-all">
                  {localStorageData.auth_user || "null"}
                </span>
              </div>
            </div>
          </div>

          {/* Login/Logout Forms */}
          {!isAuthenticated ? (
            <div className="mb-8">
              <h2 className="text-lg font-semibold text-gray-800 mb-4">
                Login Test
              </h2>

              {step === "phone" ? (
                <form onSubmit={handleLogin} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Phone Number
                    </label>
                    <input
                      type="tel"
                      value={phoneNumber}
                      onChange={(e) => setPhoneNumber(e.target.value)}
                      placeholder="0712345678"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                  <button
                    type="submit"
                    className="w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    Send OTP
                  </button>
                </form>
              ) : (
                <form onSubmit={handleVerifyOtp} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      OTP Code
                    </label>
                    <input
                      type="text"
                      value={otpCode}
                      onChange={(e) => setOtpCode(e.target.value)}
                      placeholder="Enter OTP"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                  <div className="flex space-x-2">
                    <button
                      type="submit"
                      className="flex-1 bg-green-500 text-white py-2 px-4 rounded-md hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500"
                    >
                      Verify OTP
                    </button>
                    <button
                      type="button"
                      onClick={() => setStep("phone")}
                      className="flex-1 bg-gray-500 text-white py-2 px-4 rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500"
                    >
                      Back
                    </button>
                  </div>
                </form>
              )}
            </div>
          ) : (
            <div className="mb-8">
              <h2 className="text-lg font-semibold text-gray-800 mb-4">
                Logout Test
              </h2>
              <button
                onClick={handleLogout}
                className="w-full bg-red-500 text-white py-2 px-4 rounded-md hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500"
              >
                Logout
              </button>
            </div>
          )}

          {/* Messages */}
          {error && (
            <div className="mb-4 p-3 bg-red-100 border border-red-300 text-red-700 rounded-md">
              {error}
            </div>
          )}
          {message && (
            <div className="mb-4 p-3 bg-green-100 border border-green-300 text-green-700 rounded-md">
              {message}
            </div>
          )}

          {/* Test Instructions */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h3 className="text-sm font-medium text-yellow-800 mb-2">
              🔧 Test Instructions
            </h3>
            <ol className="text-sm text-yellow-700 space-y-1 list-decimal list-inside">
              <li>Login with a phone number (e.g., 0712345678)</li>
              <li>Enter any OTP code to complete login</li>
              <li>
                Check that localStorage contains auth_token and
                auth_refresh_token
              </li>
              <li>
                <strong>Single refresh test</strong> - refresh once, you should
                remain logged in
              </li>
              <li>
                <strong>Rapid refresh test</strong> - press F5 or Ctrl+R rapidly
                5-10 times in succession
              </li>
              <li>
                <strong>Multiple tabs test</strong> - open this page in multiple
                tabs simultaneously
              </li>
              <li>
                Check browser console for authentication coordination logs
              </li>
              <li>Test logout to ensure localStorage is cleared properly</li>
            </ol>
          </div>

          {/* Rapid Refresh Test Button */}
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <h3 className="text-sm font-medium text-red-800 mb-2">
              ⚡ Rapid Refresh Simulation
            </h3>
            <p className="text-sm text-red-700 mb-3">
              This button simulates rapid page refreshes by forcing multiple
              authentication checks. Use this to test the race condition fix.
            </p>
            <button
              onClick={() => {
                console.log("🧪 Starting rapid refresh simulation...");
                // Force multiple authentication checks
                for (let i = 0; i < 5; i++) {
                  setTimeout(() => {
                    console.log(`🧪 Simulated refresh ${i + 1}/5`);
                    window.location.reload();
                  }, i * 100);
                }
              }}
              className="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500"
            >
              Simulate Rapid Refreshes
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestAuthPage;
