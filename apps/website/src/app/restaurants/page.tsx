'use client'

import { useState, useEffect } from 'react'
import { 
  MagnifyingGlassIcon, 
  FunnelIcon, 
  MapPinIcon,
  StarIcon,
  HeartIcon,
  ShareIcon,
  ViewColumnsIcon,
  Squares2X2Icon
} from '@heroicons/react/24/outline'
import { Restaurant } from '@/lib/api'
import apiService from '@/lib/api'
import RestaurantMap from '@/components/RestaurantMap'
import Header from '@/components/Header'
import Footer from '@/components/Footer'
import Link from 'next/link'

export default function RestaurantsPage() {
  const [restaurants, setRestaurants] = useState<Restaurant[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Helper function to safely get rating as number
  const getRating = (rating: any): number => {
    return typeof rating === 'number' ? rating : Number(rating || 0)
  }
  const [selectedRestaurant, setSelectedRestaurant] = useState<Restaurant | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [viewMode, setViewMode] = useState<'list' | 'grid' | 'map'>('list')
  const [showFilters, setShowFilters] = useState(false)
  const [filters, setFilters] = useState({
    category: '',
    rating: '',
    priceRange: '',
    location: ''
  })

  // Fetch restaurants from API
  useEffect(() => {
    const fetchRestaurants = async () => {
      try {
        setLoading(true)
        const response = await apiService.getRestaurants({})
        const data = Array.isArray(response) ? response : response.data || []
        setRestaurants(data)
      } catch (err) {
        console.error('Error fetching restaurants:', err)
        setError('خطا در بارگذاری رستوران‌ها')
      } finally {
        setLoading(false)
      }
    }

    fetchRestaurants()
  }, [])

  // Filter restaurants based on search and filters
  const filteredRestaurants = restaurants.filter((restaurant: Restaurant) => {
    const matchesSearch = !searchTerm || 
      restaurant.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (restaurant.description || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
      restaurant.city.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesCategory = !filters.category || 
      restaurant.categories?.some((cat: any) => cat.name === filters.category)

         const matchesRating = !filters.rating ||
       (filters.rating === '4+' && getRating(restaurant.avg_rating) >= 4) ||
       (filters.rating === '3+' && getRating(restaurant.avg_rating) >= 3)

    const matchesPrice = !filters.priceRange ||
      restaurant.price_range.toString() === filters.priceRange

    const matchesLocation = !filters.location ||
      restaurant.city === filters.location

    return matchesSearch && matchesCategory && matchesRating && matchesPrice && matchesLocation
  })

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    // Search is handled by the filter above
  }

  const handleRestaurantSelect = (restaurant: Restaurant) => {
    setSelectedRestaurant(restaurant)
  }

  const toggleFavorite = (restaurantId: string) => {
    // TODO: Implement favorite functionality with API
    console.log('Toggle favorite for:', restaurantId)
  }

  const shareRestaurant = (restaurant: Restaurant) => {
    if (navigator.share) {
      navigator.share({
        title: restaurant.name,
        text: restaurant.description || '',
        url: `/restaurants/${restaurant.id}`
      })
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <Header />
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse space-y-4">
            <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div>
            <div className="h-64 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="h-64 bg-gray-200 dark:bg-gray-700 rounded"></div>
              ))}
            </div>
          </div>
        </main>
        <Footer />
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <Header />
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <p className="text-red-600 dark:text-red-400 text-lg">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="mt-4 px-6 py-3 bg-primary-500 hover:bg-primary-600 text-white font-semibold rounded-lg transition-colors"
            >
              تلاش مجدد
            </button>
          </div>
        </main>
        <Footer />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header />
      
      {/* Search Header */}
      <div className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4 rtl:space-x-reverse">
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">رستوران‌ها</h1>
              <span className="text-gray-500 dark:text-gray-400">({filteredRestaurants.length} نتیجه)</span>
            </div>

            <div className="flex items-center space-x-3 rtl:space-x-reverse">
              {/* View Mode Toggle */}
              <div className="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
                <button
                  onClick={() => setViewMode('list')}
                  className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                    viewMode === 'list' 
                      ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm' 
                      : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
                  }`}
                >
                  لیست
                </button>
                <button
                  onClick={() => setViewMode('grid')}
                  className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                    viewMode === 'grid' 
                      ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm' 
                      : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
                  }`}
                >
                  شبکه
                </button>
                <button
                  onClick={() => setViewMode('map')}
                  className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                    viewMode === 'map' 
                      ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm' 
                      : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
                  }`}
                >
                  نقشه
                </button>
              </div>
              
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                <FunnelIcon className="h-4 w-4" />
                <span>فیلتر</span>
              </button>
            </div>
          </div>

          {/* Search Bar */}
          <form onSubmit={handleSearch} className="mt-4">
            <div className="relative max-w-lg">
              <MagnifyingGlassIcon className="absolute left-3 rtl:right-3 rtl:left-auto top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="جستجوی رستوران، غذا یا منطقه..."
                className="w-full pl-10 rtl:pr-10 rtl:pl-4 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              />
            </div>
          </form>

          {/* Quick Filters */}
          {showFilters && (
            <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4">
              <select 
                className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                value={filters.category}
                onChange={(e) => setFilters({...filters, category: e.target.value})}
              >
                <option value="">همه دسته‌ها</option>
                <option value="افغانی">افغانی</option>
                <option value="کافه">کافه</option>
                <option value="فست‌فود">فست‌فود</option>
              </select>
              
              <select 
                className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                value={filters.rating}
                onChange={(e) => setFilters({...filters, rating: e.target.value})}
              >
                <option value="">همه امتیازها</option>
                <option value="4+">4+ ستاره</option>
                <option value="3+">3+ ستاره</option>
              </select>
              
              <select 
                className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                value={filters.priceRange}
                onChange={(e) => setFilters({...filters, priceRange: e.target.value})}
              >
                <option value="">همه قیمت‌ها</option>
                <option value="1">ارزان ($)</option>
                <option value="2">متوسط ($$)</option>
                <option value="3">گران ($$$)</option>
              </select>
              
              <select 
                className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                value={filters.location}
                onChange={(e) => setFilters({...filters, location: e.target.value})}
              >
                <option value="">همه شهرها</option>
                <option value="کابل">کابل</option>
                <option value="هرات">هرات</option>
                <option value="مزار شریف">مزار شریف</option>
              </select>
            </div>
          )}
        </div>
      </div>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {viewMode === 'map' ? (
          /* Map View */
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden" style={{ height: '600px' }}>
            <RestaurantMap
              restaurants={filteredRestaurants}
              onRestaurantSelect={handleRestaurantSelect}
              height="600px"
            />
          </div>
        ) : (
          /* List/Grid View */
          <div className={`grid gap-6 ${
            viewMode === 'grid' 
              ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' 
              : 'grid-cols-1'
          }`}>
            {filteredRestaurants.map((restaurant) => (
              <div 
                key={restaurant.id}
                className={`bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-all duration-300 ${
                  selectedRestaurant?.id === restaurant.id ? 'ring-2 ring-primary-500 border-primary-200' : ''
                } ${viewMode === 'list' ? 'flex' : ''}`}
                onClick={() => setSelectedRestaurant(restaurant)}
              >
                {/* Restaurant Image */}
                <div className={`bg-gray-200 dark:bg-gray-700 overflow-hidden ${
                  viewMode === 'list' 
                    ? 'flex-shrink-0 w-32 h-32 rounded-l-xl' 
                    : 'w-full h-48 rounded-t-xl'
                }`}>
                  {restaurant.photos && restaurant.photos.length > 0 ? (
                    <img
                      src={restaurant.photos[0].url}
                      alt={restaurant.photos[0].alt_text || restaurant.name}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      <span className="text-gray-400 text-sm">بدون تصویر</span>
                    </div>
                  )}
                </div>

                {/* Restaurant Info */}
                <div className="p-6 flex-1">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex-1 min-w-0">
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white truncate">
                        {restaurant.name}
                      </h3>
                      <div className="flex items-center mt-1">
                        <div className="flex items-center">
                                                      {[...Array(5)].map((_, i) => (
                              <StarIcon
                                key={i}
                                className={`h-4 w-4 ${
                                  i < Math.floor(getRating(restaurant.avg_rating))
                                    ? 'text-yellow-400 fill-current'
                                    : 'text-gray-300'
                                }`}
                              />
                            ))}
                        </div>
                        <span className="mr-2 rtl:ml-2 rtl:mr-0 text-sm text-gray-600 dark:text-gray-400">
                          {getRating(restaurant.avg_rating).toFixed(1)} ({restaurant.reviews?.length || 0} نظر)
                        </span>
                        <span className="mr-2 rtl:ml-2 rtl:mr-0 text-sm font-medium text-gray-900 dark:text-white">
                          {'$'.repeat(restaurant.price_range)}
                        </span>
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          toggleFavorite(restaurant.id)
                        }}
                        className="p-2 text-gray-400 hover:text-red-500 transition-colors"
                      >
                        <HeartIcon className="h-5 w-5" />
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          shareRestaurant(restaurant)
                        }}
                        className="p-2 text-gray-400 hover:text-blue-500 transition-colors"
                      >
                        <ShareIcon className="h-5 w-5" />
                      </button>
                    </div>
                  </div>

                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
                    {restaurant.description || 'توضیحی موجود نیست'}
                  </p>

                  <div className="flex items-center text-sm text-gray-500 dark:text-gray-400 mb-4">
                    <MapPinIcon className="h-4 w-4 mr-1" />
                    <span>{restaurant.address || `${restaurant.city}, ${restaurant.state}`}</span>
                  </div>

                  <Link
                    href={`/restaurants/${restaurant.id}`}
                    className="block w-full bg-primary-50 hover:bg-primary-100 dark:bg-primary-900/20 dark:hover:bg-primary-900/30 text-primary-600 dark:text-primary-400 font-semibold py-2 px-4 rounded-lg transition-colors duration-200 text-center"
                    onClick={(e) => e.stopPropagation()}
                  >
                    مشاهده جزئیات
                  </Link>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Empty State */}
        {filteredRestaurants.length === 0 && (
          <div className="text-center py-12">
            <MapPinIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              رستوران یافت نشد
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              لطفاً جستجو یا فیلترهای خود را تغییر دهید
            </p>
          </div>
        )}
      </main>

      <Footer />
    </div>
  )
} 