'use client';

import React, { useState } from 'react';
import {
  AppBar,
  Toolbar,
  Typography,
  Box,
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  IconButton,
  Avatar,
  Menu,
  MenuItem,
  Divider,
  Badge,
  Tooltip,
  useTheme,
  useMediaQuery,
  Chip,
  Button,
  Card,
  CardContent,
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  Restaurant as RestaurantIcon,
  People as PeopleIcon,
  MenuBook as MenuIcon,
  Receipt as OrderIcon,
  EventSeat as ReservationIcon,
  Star as ReviewIcon,
  Category as CategoryIcon,
  Menu as MenuIconHamburger,
  Notifications as NotificationsIcon,
  Settings as SettingsIcon,
  Logout as LogoutIcon,
  AccountCircle as AccountIcon,
  Add as AddIcon,
  LightMode as LightModeIcon,
  DarkMode as DarkModeIcon,
  Analytics as AnalyticsIcon,
} from '@mui/icons-material';
import { useRouter, usePathname } from 'next/navigation';
import { useGetIdentity, useLogout } from '@refinedev/core';
import { useColorMode } from '../contexts/colorMode';

const drawerWidth = 280;

const menuItems = [
  {
    text: 'داشبورد',
    icon: <DashboardIcon />,
    path: '/',
    roles: ['admin', 'restaurant_owner'],
  },
  {
    text: 'آنالیتیکس',
    icon: <AnalyticsIcon />,
    path: '/analytics/dashboard',
    roles: ['admin', 'restaurant_owner'],
  },
  {
    text: 'رستوران‌ها',
    icon: <RestaurantIcon />,
    path: '/restaurants/list',
    roles: ['admin', 'restaurant_owner'],
  },
  {
    text: 'کاربران',
    icon: <PeopleIcon />,
    path: '/users/list',
    roles: ['admin'],
  },
  {
    text: 'منوها',
    icon: <MenuIcon />,
    path: '/menu-items/list',
    roles: ['admin', 'restaurant_owner'],
  },
  {
    text: 'سفارشات',
    icon: <OrderIcon />,
    path: '/orders/list',
    roles: ['admin', 'restaurant_owner'],
  },
  // { text: "رزروها", icon: <ReservationIcon />, path: "/reservations/list", roles: ["admin", "restaurant_owner"] }, // DISABLED
  {
    text: 'نظرات',
    icon: <ReviewIcon />,
    path: '/reviews/list',
    roles: ['admin', 'restaurant_owner'],
  },
  {
    text: 'دسته‌بندی‌ها',
    icon: <CategoryIcon />,
    path: '/categories/list',
    roles: ['admin'],
  },
  {
    text: 'درخواست‌های ثبت',
    icon: <RestaurantIcon />,
    path: '/restaurant-registrations/list',
    roles: ['admin'],
  },
];

export function Layout({ children }: { children: React.ReactNode }) {
  const [mobileOpen, setMobileOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const router = useRouter();
  const pathname = usePathname();
  const { mode, toggleColorMode } = useColorMode();

  const { data: identity, isLoading: identityLoading } = useGetIdentity();
  const userRole = (identity as any)?.role;

  // Filter menu items based on user role
  const filteredMenuItems = menuItems.filter(item =>
    item.roles.includes(userRole)
  );

  const { mutate: logout } = useLogout();

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleProfileMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleProfileMenuClose = () => {
    setAnchorEl(null);
  };

  const handleNavigation = (path: string) => {
    router.push(path);
    if (isMobile) {
      setMobileOpen(false);
    }
  };

  const handleLogout = () => {
    logout();
    handleProfileMenuClose();
  };

  const getUserInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin':
        return 'error';
      case 'restaurant_owner':
        return 'primary';
      default:
        return 'default';
    }
  };

  const getRoleLabel = (role: string) => {
    switch (role) {
      case 'admin':
        return 'مدیر';
      case 'restaurant_owner':
        return 'صاحب رستوران';
      default:
        return 'کاربر';
    }
  };

  // Materialize theme colors - always light on dark sidebar
  const sidebarTextColor = 'rgba(234, 234, 255, 0.87)'; // Always light text
  const sidebarIconColor = 'rgba(234, 234, 255, 0.68)'; // Always light icons

  const drawer = (
    <Box
      sx={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        background: '#2F3349', // Always use semi-dark background like Materialize demo
        // No border - clean drawer style like the demo
      }}
    >
      {/* Logo/Brand Section - Clean Materialize Style */}
      <Box
        sx={{
          p: 3,
          borderBottom: '1px solid rgba(234, 234, 255, 0.12)',
          textAlign: 'right', // RTL alignment
          // Clean background that matches the drawer
          backgroundColor: 'transparent',
          color: '#EAEAFF', // Always light text on dark background
        }}
      >
        <Typography
          variant="h4"
          fontWeight="bold"
          sx={{
            mb: 0.5,
            fontFamily: 'var(--font-inter)',
            letterSpacing: '-0.5px',
            color: 'inherit',
          }}
        >
          🍽️ از کُجا
        </Typography>
        <Typography
          variant="body2"
          sx={{
            opacity: 0.7,
            fontSize: '0.8125rem',
            fontWeight: 400,
            color: 'inherit',
          }}
        >
          پنل مدیریت رستوران
        </Typography>
      </Box>

      {/* Navigation Items - Materialize Style */}
      <Box sx={{ flex: 1, overflow: 'auto', px: 2, py: 2, textAlign: 'right' }}>
        {' '}
        {/* RTL text alignment */}
        <List sx={{ p: 0, direction: 'rtl', textAlign: 'right' }}>
          {' '}
          {/* RTL for the entire list */}
          {filteredMenuItems.map(item => {
            const isActive =
              pathname === item.path ||
              (item.path !== '/' && pathname.startsWith(item.path));
            return (
              <ListItem
                key={item.text}
                onClick={() => handleNavigation(item.path)}
                sx={{
                  borderRadius: '12px',
                  mb: 1,
                  cursor: 'pointer',
                  minHeight: '44px',
                  position: 'relative',
                  transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
                  direction: 'rtl', // RTL direction for the entire item
                  textAlign: 'right', // Right alignment

                  // Active state
                  ...(isActive && {
                    backgroundColor: '#666CFF',
                    color: '#FFFFFF',
                    boxShadow: '0 2px 4px 0 rgba(102, 108, 255, 0.4)',
                    '&::before': {
                      content: '""',
                      position: 'absolute',
                      right: 0,
                      top: '50%',
                      transform: 'translateY(-50%)',
                      width: '3px',
                      height: '20px',
                      backgroundColor: '#FFFFFF',
                      borderRadius: '4px 0 0 4px',
                    },
                  }),

                  // Non-active state
                  ...(!isActive && {
                    backgroundColor: 'transparent',
                    color: sidebarTextColor,
                    '&:hover': {
                      backgroundColor: 'rgba(234, 234, 255, 0.08)', // Always light hover on dark background
                      transform: 'translateX(4px)',
                    },
                  }),

                  '&:active': {
                    transform: 'scale(0.98)',
                  },
                }}
              >
                <ListItemIcon
                  sx={{
                    color: isActive ? '#FFFFFF' : sidebarIconColor,
                    minWidth: '22px',
                    mr: 2,
                    transition: 'color 0.2s ease',
                    '& .MuiSvgIcon-root': {
                      fontSize: '22px',
                    },
                  }}
                >
                  {item.icon}
                </ListItemIcon>
                <ListItemText
                  primary={item.text}
                  primaryTypographyProps={{
                    fontSize: '0.9375rem',
                    fontWeight: isActive ? 600 : 500,
                    color: isActive ? '#FFFFFF' : sidebarTextColor,
                    fontFamily: 'var(--font-inter)',
                    lineHeight: 1.467,
                    letterSpacing: '0.15px',
                    textAlign: 'right', // RTL text alignment
                  }}
                  sx={{
                    textAlign: 'right', // Additional RTL alignment
                    direction: 'rtl', // RTL direction
                  }}
                />
              </ListItem>
            );
          })}
        </List>
        {/* Quick Actions Section - Materialize Style */}
        <Box sx={{ mt: 4, px: 2 }}>
          <Typography
            variant="overline"
            sx={{
              color: 'rgba(234, 234, 255, 0.38)', // Always light text on dark background
              fontSize: '0.75rem',
              fontWeight: 600,
              letterSpacing: '1px',
              mb: 2,
              display: 'block',
            }}
          >
            عملیات سریع
          </Typography>
          <Card
            sx={{
              backgroundColor: 'rgba(234, 234, 255, 0.04)', // Always light background on dark sidebar
              border: '1px solid rgba(234, 234, 255, 0.12)', // Always light border
              borderRadius: '12px',
              boxShadow: 'none',
            }}
          >
            <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
              <Typography
                variant="body2"
                sx={{
                  color: sidebarTextColor,
                  fontSize: '0.8125rem',
                  fontWeight: 500,
                  mb: 2,
                }}
              >
                افزودن سریع
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                <Chip
                  icon={<RestaurantIcon />}
                  label="رستوران"
                  size="small"
                  onClick={() => handleNavigation('/restaurants/create')}
                  sx={{
                    backgroundColor: 'rgba(234, 234, 255, 0.08)', // Always light background on dark sidebar
                    color: sidebarTextColor,
                    border: 'none',
                    fontSize: '0.75rem',
                    height: '28px',
                    '&:hover': {
                      backgroundColor: '#666CFF',
                      color: '#FFFFFF',
                    },
                    '& .MuiChip-icon': {
                      fontSize: '16px',
                    },
                  }}
                />
                <Chip
                  icon={<MenuIcon />}
                  label="منو"
                  size="small"
                  onClick={() => handleNavigation('/menu-items/create')}
                  sx={{
                    backgroundColor: 'rgba(234, 234, 255, 0.08)', // Always light background on dark sidebar
                    color: sidebarTextColor,
                    border: 'none',
                    fontSize: '0.75rem',
                    height: '28px',
                    '&:hover': {
                      backgroundColor: '#666CFF',
                      color: '#FFFFFF',
                    },
                    '& .MuiChip-icon': {
                      fontSize: '16px',
                    },
                  }}
                />
              </Box>
            </CardContent>
          </Card>
        </Box>
      </Box>

      {/* User Profile Section - Materialize Style */}
      <Box
        sx={{
          p: 2,
          borderTop: '1px solid rgba(234, 234, 255, 0.12)', // Always light border on dark background
          mt: 'auto',
        }}
      >
        {!identityLoading && (identity as any) && (
          <Card
            sx={{
              backgroundColor: 'rgba(234, 234, 255, 0.04)', // Always light background on dark sidebar
              border: '1px solid rgba(234, 234, 255, 0.12)', // Always light border
              borderRadius: '12px',
              cursor: 'pointer',
              transition: 'all 0.2s ease',
              '&:hover': {
                backgroundColor: 'rgba(234, 234, 255, 0.08)', // Always light hover on dark background
              },
            }}
            onClick={handleProfileMenuOpen}
          >
            <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Avatar
                  sx={{
                    width: 40,
                    height: 40,
                    backgroundColor: '#666CFF',
                    color: 'white',
                    fontSize: '0.875rem',
                    fontWeight: 600,
                  }}
                >
                  {getUserInitials((identity as any)?.name || 'User')}
                </Avatar>
                <Box sx={{ flex: 1, minWidth: 0 }}>
                  <Typography
                    variant="body2"
                    sx={{
                      color: sidebarTextColor,
                      fontWeight: 600,
                      fontSize: '0.875rem',
                      lineHeight: 1.43,
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                    }}
                  >
                    {(identity as any)?.name || 'User'}
                  </Typography>
                  <Chip
                    label={getRoleLabel((identity as any)?.role || 'user')}
                    size="small"
                    color={getRoleColor((identity as any)?.role || 'user')}
                    sx={{
                      height: '20px',
                      fontSize: '0.6875rem',
                      fontWeight: 500,
                      mt: 0.5,
                    }}
                  />
                </Box>
              </Box>
            </CardContent>
          </Card>
        )}
      </Box>
    </Box>
  );

  return (
    <Box
      sx={{
        display: 'flex',
        minHeight: '100vh',
        bgcolor: 'background.default',
        direction: 'rtl',
      }}
    >
      {/* App Bar - Enhanced for RTL */}
      <AppBar
        position="fixed"
        sx={{
          width: { md: `calc(100% - ${drawerWidth}px)` },
          mr: { md: `${drawerWidth}px` }, // RTL: margin-right instead of margin-left
          bgcolor: 'background.paper',
          color: 'text.primary',
          boxShadow: '0 2px 6px 0 rgba(58, 53, 65, 0.12)',
          borderBottom: '1px solid rgba(58, 53, 65, 0.12)',
          borderRadius: 0, // Sharp corners like Materialize demo
          backdropFilter: 'blur(8px)',
        }}
      >
        <Toolbar sx={{ justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <IconButton
              color="inherit"
              aria-label="open drawer"
              edge="start"
              onClick={handleDrawerToggle}
              sx={{ ml: 2, display: { md: 'none' } }} // RTL: margin-left instead of margin-right
            >
              <MenuIconHamburger />
            </IconButton>

            <Box>
              <Typography variant="h6" noWrap component="div" fontWeight={600}>
                {filteredMenuItems.find(item => item.path === pathname)?.text ||
                  'داشبورد'}
              </Typography>
              <Typography variant="caption" color="textSecondary">
                سیستم مدیریت رستوران از کُجا
              </Typography>
            </Box>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {/* Theme Toggle */}
            <Tooltip title={mode === 'light' ? 'حالت تاریک' : 'حالت روشن'}>
              <IconButton
                onClick={toggleColorMode}
                color="inherit"
                sx={{
                  borderRadius: 2,
                  '&:hover': { bgcolor: 'action.hover' },
                }}
              >
                {mode === 'light' ? <DarkModeIcon /> : <LightModeIcon />}
              </IconButton>
            </Tooltip>

            {/* Notifications */}
            <Tooltip title="اعلان‌ها">
              <IconButton
                color="inherit"
                sx={{
                  borderRadius: 2,
                  '&:hover': { bgcolor: 'action.hover' },
                }}
              >
                <Badge badgeContent={3} color="error">
                  <NotificationsIcon />
                </Badge>
              </IconButton>
            </Tooltip>

            {/* User Profile */}
            <Tooltip title="تنظیمات حساب">
              <IconButton
                onClick={handleProfileMenuOpen}
                sx={{
                  borderRadius: 2,
                  '&:hover': { bgcolor: 'action.hover' },
                }}
                aria-controls={Boolean(anchorEl) ? 'account-menu' : undefined}
                aria-haspopup="true"
                aria-expanded={Boolean(anchorEl) ? 'true' : undefined}
              >
                {identityLoading ? (
                  <Avatar sx={{ width: 36, height: 36 }} />
                ) : (
                  <Avatar
                    sx={{
                      width: 36,
                      height: 36,
                      bgcolor: 'primary.main',
                      background:
                        'linear-gradient(135deg, #666CFF 0%, #8589FF 100%)',
                    }}
                  >
                    {identity
                      ? getUserInitials((identity as any)?.name || 'کاربر')
                      : 'ک'}
                  </Avatar>
                )}
              </IconButton>
            </Tooltip>

            <Menu
              anchorEl={anchorEl}
              id="account-menu"
              open={Boolean(anchorEl)}
              onClose={handleProfileMenuClose}
              onClick={handleProfileMenuClose}
              PaperProps={{
                elevation: 8,
                sx: {
                  overflow: 'visible',
                  filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.12))',
                  mt: 1.5,
                  minWidth: 220,
                  borderRadius: 3,
                  border: '1px solid',
                  borderColor: 'divider',
                  '& .MuiAvatar-root': {
                    width: 32,
                    height: 32,
                    mr: -0.5, // RTL: margin-right instead of margin-left
                    ml: 1, // RTL: margin-left instead of margin-right
                  },
                  '&:before': {
                    content: '""',
                    display: 'block',
                    position: 'absolute',
                    top: 0,
                    left: 14, // RTL: left instead of right
                    width: 10,
                    height: 10,
                    bgcolor: 'background.paper',
                    transform: 'translateY(-50%) rotate(45deg)',
                    zIndex: 0,
                    border: '1px solid',
                    borderColor: 'divider',
                    borderBottom: 'none',
                    borderRight: 'none', // RTL: border-right instead of border-left
                  },
                },
              }}
              transformOrigin={{ horizontal: 'left', vertical: 'top' }} // RTL: left instead of right
              anchorOrigin={{ horizontal: 'left', vertical: 'bottom' }} // RTL: left instead of right
            >
              {identity ? (
                <Box
                  sx={{
                    px: 2,
                    py: 1.5,
                    borderBottom: 1,
                    borderColor: 'divider',
                  }}
                >
                  <Typography variant="subtitle2" fontWeight={600}>
                    {(identity as any)?.name || 'User'}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    {(identity as any)?.email ||
                      (identity as any)?.phone ||
                      '<EMAIL>'}
                  </Typography>
                  <Chip
                    label={getRoleLabel((identity as any)?.role || 'user')}
                    size="small"
                    color={getRoleColor((identity as any)?.role || 'user')}
                    sx={{ mt: 1 }}
                  />
                </Box>
              ) : null}
              <MenuItem onClick={handleProfileMenuClose} sx={{ py: 1.5 }}>
                <AccountIcon sx={{ ml: 2 }} />{' '}
                {/* RTL: margin-left instead of margin-right */}
                پروفایل
              </MenuItem>
              <MenuItem onClick={handleProfileMenuClose} sx={{ py: 1.5 }}>
                <SettingsIcon sx={{ ml: 2 }} />{' '}
                {/* RTL: margin-left instead of margin-right */}
                تنظیمات
              </MenuItem>
              <Divider />
              <MenuItem
                onClick={handleLogout}
                sx={{ color: 'error.main', py: 1.5 }}
              >
                <LogoutIcon sx={{ ml: 2 }} />{' '}
                {/* RTL: margin-left instead of margin-right */}
                خروج
              </MenuItem>
            </Menu>
          </Box>
        </Toolbar>
      </AppBar>

      {/* Navigation Drawer - Enhanced for RTL */}
      <Box
        component="nav"
        sx={{ width: { md: drawerWidth }, flexShrink: { md: 0 } }}
      >
        {/* Mobile drawer */}
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{
            keepMounted: true,
          }}
          sx={{
            display: { xs: 'block', md: 'none' },
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: drawerWidth,
              border: 'none', // Clean drawer style like Materialize demo
              borderRadius: 0, // Sharp corners like Materialize demo
              backgroundColor: '#2F3349', // Always semi-dark like Materialize demo
              color: '#EAEAFF', // Always light text on dark background
              boxShadow: '0 4px 8px -4px rgba(58, 53, 65, 0.42)',
            },
          }}
        >
          {drawer}
        </Drawer>

        {/* Desktop drawer */}
        <Drawer
          variant="permanent"
          sx={{
            display: { xs: 'none', md: 'block' },
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: drawerWidth,
              border: 'none', // Clean drawer style like Materialize demo
              borderRadius: 0, // Sharp corners like Materialize demo
              backgroundColor: '#2F3349', // Always semi-dark like Materialize demo
              color: '#EAEAFF', // Always light text on dark background
              boxShadow: '0 0 10px 0 rgba(58, 53, 65, 0.1)', // Subtle shadow like the demo
            },
          }}
          open
          anchor="right" // RTL: anchor to right
        >
          {drawer}
        </Drawer>
      </Box>

      {/* Main Content - Enhanced for RTL */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          width: { md: `calc(100% - ${drawerWidth}px)` },
          minHeight: '100vh',
          bgcolor: 'background.default',
          position: 'relative',
          direction: 'rtl', // Ensure RTL direction
        }}
      >
        {/* Role-based banner for restaurant owners */}
        {userRole === 'restaurant_owner' && (
          <Box
            sx={{
              bgcolor: 'info.main',
              color: 'white',
              p: 2,
              textAlign: 'center',
              borderRadius: 0,
            }}
          >
            <Typography variant="body2">
              شما در حال استفاده از پنل مدیریت محدود هستید. برای تجربه بهتر، از
              <Button
                variant="text"
                sx={{ color: 'white', textDecoration: 'underline', mx: 1 }}
                onClick={() =>
                  window.open(
                    'http://localhost:3000/restaurant-dashboard',
                    '_blank'
                  )
                }
              >
                داشبورد اصلی رستوران
              </Button>
              استفاده کنید.
            </Typography>
          </Box>
        )}

        <Toolbar />
        <Box sx={{ p: 3 }}>{children}</Box>
      </Box>
    </Box>
  );
}
