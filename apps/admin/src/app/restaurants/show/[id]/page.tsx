'use client';

import React from 'react';
import {
  Show,
  TextFieldComponent as TextField,
  EmailField,
  UrlField,
  BooleanField,
  TagField,
  NumberField,
} from '@refinedev/mui';
import {
  Stack,
  Typography,
  Grid,
  Card,
  CardContent,
  Chip,
  Box,
  Divider,
  Avatar,
  Rating,
} from '@mui/material';
import {
  LocationOn,
  Phone,
  Email,
  Language,
  Schedule,
  Star,
  AttachMoney,
  Person,
  Restaurant as RestaurantIcon,
} from '@mui/icons-material';
import { useShow, useMany } from '@refinedev/core';

// Force dynamic rendering to prevent SSG issues
export const dynamic = 'force-dynamic';

export default function RestaurantShow() {
  const { queryResult } = useShow({
    resource: 'restaurants',
  });

  const { data, isLoading } = queryResult;
  const record = data?.data;

  const categoryIds =
    record?.categories?.map((item: any) => item.id).filter(Boolean) ?? [];
  const { data: categoriesData } = useMany({
    resource: 'categories',
    ids: categoryIds,
  });

  const ownerIds = record?.owner?.id ? [record.owner.id] : [];
  const { data: ownerData } = useMany({
    resource: 'users',
    ids: ownerIds,
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'pending':
        return 'warning';
      case 'suspended':
        return 'error';
      default:
        return 'default';
    }
  };

  const getPriceRangeText = (range: number) => {
    switch (range) {
      case 1:
        return '$ (Budget)';
      case 2:
        return '$$ (Moderate)';
      case 3:
        return '$$$ (Expensive)';
      case 4:
        return '$$$$ (Very Expensive)';
      default:
        return 'Not specified';
    }
  };

  const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <Show isLoading={isLoading}>
      <Stack gap={3}>
        {/* Header Card */}
        <Card>
          <CardContent>
            <Box display="flex" alignItems="center" gap={2} mb={2}>
              <Avatar sx={{ bgcolor: 'primary.main', width: 56, height: 56 }}>
                <RestaurantIcon />
              </Avatar>
              <Box>
                <Typography variant="h4" component="h1" gutterBottom>
                  {record?.name}
                </Typography>
                <Box display="flex" alignItems="center" gap={2}>
                  <Chip
                    label={record?.status}
                    color={getStatusColor(record?.status)}
                    size="small"
                  />
                  {record?.is_featured && (
                    <Chip label="Featured" color="primary" size="small" />
                  )}
                  <Box display="flex" alignItems="center" gap={0.5}>
                    <Star sx={{ color: 'orange', fontSize: 20 }} />
                    <Typography variant="body2">
                      {Number(record?.avg_rating || 0).toFixed(1)}
                    </Typography>
                  </Box>
                </Box>
              </Box>
            </Box>

            {record?.description && (
              <Typography variant="body1" color="text.secondary" paragraph>
                {record.description}
              </Typography>
            )}
          </CardContent>
        </Card>

        <Grid container spacing={3}>
          {/* Basic Information */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Basic Information
                </Typography>
                <Stack spacing={2}>
                  <Box>
                    <Typography variant="subtitle2" color="text.secondary">
                      Restaurant ID
                    </Typography>
                    <Typography variant="body1">{record?.id}</Typography>
                  </Box>

                  {ownerData?.data?.[0] && (
                    <Box>
                      <Typography variant="subtitle2" color="text.secondary">
                        Owner
                      </Typography>
                      <Box display="flex" alignItems="center" gap={1}>
                        <Person fontSize="small" />
                        <Typography variant="body1">
                          {ownerData.data[0].name ||
                            `${ownerData.data[0].firstName || ''} ${ownerData.data[0].lastName || ''}`.trim()}
                        </Typography>
                      </Box>
                    </Box>
                  )}

                  <Box>
                    <Typography variant="subtitle2" color="text.secondary">
                      Price Range
                    </Typography>
                    <Box display="flex" alignItems="center" gap={1}>
                      <AttachMoney fontSize="small" />
                      <Typography variant="body1">
                        {getPriceRangeText(record?.price_range)}
                      </Typography>
                    </Box>
                  </Box>

                  <Box>
                    <Typography variant="subtitle2" color="text.secondary">
                      Categories
                    </Typography>
                    <Box display="flex" flexWrap="wrap" gap={1} mt={1}>
                      {categoriesData?.data?.map((category: any) => (
                        <Chip
                          key={category.id}
                          label={category.name}
                          size="small"
                          variant="outlined"
                        />
                      ))}
                    </Box>
                  </Box>
                </Stack>
              </CardContent>
            </Card>
          </Grid>

          {/* Location Information */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Location
                </Typography>
                <Stack spacing={2}>
                  <Box>
                    <Typography variant="subtitle2" color="text.secondary">
                      Address
                    </Typography>
                    <Box display="flex" alignItems="flex-start" gap={1}>
                      <LocationOn fontSize="small" sx={{ mt: 0.5 }} />
                      <Box>
                        <Typography variant="body1">
                          {record?.address}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {record?.city}, {record?.state} {record?.zip_code}
                        </Typography>
                      </Box>
                    </Box>
                  </Box>

                  {record?.latitude && record?.longitude && (
                    <Box>
                      <Typography variant="subtitle2" color="text.secondary">
                        Coordinates
                      </Typography>
                      <Typography variant="body1">
                        {record.latitude}, {record.longitude}
                      </Typography>
                    </Box>
                  )}
                </Stack>
              </CardContent>
            </Card>
          </Grid>

          {/* Contact Information */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Contact Information
                </Typography>
                <Stack spacing={2}>
                  <Box>
                    <Typography variant="subtitle2" color="text.secondary">
                      Phone
                    </Typography>
                    <Box display="flex" alignItems="center" gap={1}>
                      <Phone fontSize="small" />
                      <Typography variant="body1">{record?.phone}</Typography>
                    </Box>
                  </Box>

                  {record?.email && (
                    <Box>
                      <Typography variant="subtitle2" color="text.secondary">
                        Email
                      </Typography>
                      <Box display="flex" alignItems="center" gap={1}>
                        <Email fontSize="small" />
                        <EmailField value={record.email} />
                      </Box>
                    </Box>
                  )}

                  {record?.website && (
                    <Box>
                      <Typography variant="subtitle2" color="text.secondary">
                        Website
                      </Typography>
                      <Box display="flex" alignItems="center" gap={1}>
                        <Language fontSize="small" />
                        <UrlField value={record.website} />
                      </Box>
                    </Box>
                  )}
                </Stack>
              </CardContent>
            </Card>
          </Grid>

          {/* Hours and Additional Info */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Additional Information
                </Typography>
                <Stack spacing={2}>
                  {record?.hours_of_operation && (
                    <Box>
                      <Typography variant="subtitle2" color="text.secondary">
                        Hours of Operation
                      </Typography>
                      <Box display="flex" alignItems="flex-start" gap={1}>
                        <Schedule fontSize="small" sx={{ mt: 0.5 }} />
                        <Typography
                          variant="body1"
                          style={{ whiteSpace: 'pre-line' }}
                        >
                          {record.hours_of_operation}
                        </Typography>
                      </Box>
                    </Box>
                  )}

                  <Divider />

                  <Box>
                    <Typography variant="subtitle2" color="text.secondary">
                      Created
                    </Typography>
                    <Typography variant="body1">
                      {formatDate(record?.created_at)}
                    </Typography>
                  </Box>

                  <Box>
                    <Typography variant="subtitle2" color="text.secondary">
                      Last Updated
                    </Typography>
                    <Typography variant="body1">
                      {formatDate(record?.updated_at)}
                    </Typography>
                  </Box>
                </Stack>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Stack>
    </Show>
  );
}
