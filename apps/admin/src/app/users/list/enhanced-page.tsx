'use client';

import React, { useState } from 'react';
import {
  useDataGrid,
  EditButton,
  ShowButton,
  DeleteButton,
} from '@refinedev/mui';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import {
  Box,
  Button,
  Card,
  CardContent,
  Stack,
  Typography,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  IconButton,
  Chip,
  Avatar,
  Tooltip,
  Badge,
  Paper,
} from '@mui/material';
import { useCreate, useInvalidate } from '@refinedev/core';
import {
  Add,
  Close,
  Person as PersonIcon,
  AdminPanelSettings as AdminIcon,
  Restaurant as RestaurantOwnerIcon,
  People as CustomerIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon,
  Download as ExportIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  CalendarToday as DateIcon,
} from '@mui/icons-material';
import { useForm } from '@refinedev/react-hook-form';
import { Controller } from 'react-hook-form';

// Force dynamic rendering to prevent SSG issues
export const dynamic = 'force-dynamic';

interface UserFormData {
  name: string;
  phone_number: string;
  email?: string;
  role: 'admin' | 'restaurant_owner' | 'customer';
}

export default function EnhancedUserList() {
  const invalidate = useInvalidate();
  const [openCreateModal, setOpenCreateModal] = useState(false);
  const [filterRole, setFilterRole] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const { mutate: createUser } = useCreate();

  const { dataGridProps } = useDataGrid({
    resource: 'users',
    syncWithLocation: true,
    pagination: {
      mode: 'server',
    },
    filters: {
      mode: 'server',
      initial: [
        ...(filterRole
          ? [
              {
                field: 'role',
                operator: 'eq' as const,
                value: filterRole,
              },
            ]
          : []),
        ...(searchQuery
          ? [
              {
                field: 'search',
                operator: 'contains' as const,
                value: searchQuery,
              },
            ]
          : []),
      ],
    },
    sorters: {
      mode: 'server',
      initial: [
        {
          field: 'created_at',
          order: 'desc',
        },
      ],
    },
  });

  // Form for creating user
  const {
    register,
    control,
    formState: { errors },
    handleSubmit,
    reset,
  } = useForm<UserFormData>();

  const handleCreateUser = (data: UserFormData) => {
    try {
      console.log('Creating user with data:', data);

      // Validate required fields
      if (!data.name || !data.phone_number || !data.role) {
        alert('لطفاً تمام فیلدهای الزامی را پر کنید');
        return;
      }

      // Clean the data
      const cleanData = {
        name: data.name.trim(),
        phone_number: data.phone_number.trim(),
        email: data.email?.trim() || null,
        role: data.role,
      };

      createUser(
        {
          resource: 'users',
          values: cleanData,
        },
        {
          onSuccess: response => {
            console.log('User created successfully:', response);
            setOpenCreateModal(false);
            reset();
            // Invalidate the users list to refresh data
            invalidate({
              resource: 'users',
              invalidates: ['list'],
            });
          },
          onError: error => {
            console.error('Error creating user:', error);
            alert('خطا در ایجاد کاربر: ' + error.message);
          },
        }
      );
    } catch (error) {
      console.error('Error in handleCreateUser:', error);
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'admin':
        return <AdminIcon sx={{ fontSize: 16 }} />;
      case 'restaurant_owner':
        return <RestaurantOwnerIcon sx={{ fontSize: 16 }} />;
      case 'customer':
        return <CustomerIcon sx={{ fontSize: 16 }} />;
      default:
        return <PersonIcon sx={{ fontSize: 16 }} />;
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin':
        return 'error';
      case 'restaurant_owner':
        return 'primary';
      case 'customer':
        return 'success';
      default:
        return 'default';
    }
  };

  const getRoleLabel = (role: string) => {
    switch (role) {
      case 'admin':
        return 'مدیر سیستم';
      case 'restaurant_owner':
        return 'صاحب رستوران';
      case 'customer':
        return 'مشتری';
      default:
        return 'نامشخص';
    }
  };

  const getUserInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  // Calculate user statistics
  const totalUsers = dataGridProps?.rowCount || 0;
  const adminCount =
    dataGridProps?.rows?.filter((user: any) => user.role === 'admin').length ||
    0;
  const ownerCount =
    dataGridProps?.rows?.filter((user: any) => user.role === 'restaurant_owner')
      .length || 0;
  const customerCount =
    dataGridProps?.rows?.filter((user: any) => user.role === 'customer')
      .length || 0;

  const columns: GridColDef[] = [
    {
      field: 'id',
      headerName: 'شناسه',
      type: 'number',
      minWidth: 80,
    },
    {
      field: 'name',
      headerName: 'نام کاربر',
      minWidth: 250,
      flex: 1,
      renderCell: ({ row }) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5, py: 1 }}>
          <Avatar
            sx={{
              bgcolor: getRoleColor(row.role) + '.main',
              width: 40,
              height: 40,
              fontSize: '0.875rem',
            }}
          >
            {getUserInitials(row.name || 'کاربر')}
          </Avatar>
          <Box>
            <Typography variant="subtitle2" fontWeight={600}>
              {row.name || 'نام نامشخص'}
            </Typography>
            <Box
              sx={{ display: 'flex', alignItems: 'center', gap: 0.5, mt: 0.5 }}
            >
              {getRoleIcon(row.role)}
              <Typography variant="caption" color="textSecondary">
                {getRoleLabel(row.role)}
              </Typography>
            </Box>
          </Box>
        </Box>
      ),
    },
    {
      field: 'contact',
      headerName: 'اطلاعات تماس',
      minWidth: 280,
      flex: 1,
      renderCell: ({ row }) => (
        <Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
            <PhoneIcon sx={{ fontSize: 14, color: 'text.secondary' }} />
            <Typography variant="body2">
              {row.phone_number || 'ندارد'}
            </Typography>
          </Box>
          {row.email && (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <EmailIcon sx={{ fontSize: 14, color: 'text.secondary' }} />
              <Typography variant="body2" color="textSecondary">
                {row.email}
              </Typography>
            </Box>
          )}
        </Box>
      ),
    },
    {
      field: 'role',
      headerName: 'نقش',
      minWidth: 150,
      renderCell: function render({ row }) {
        return (
          <Chip
            icon={getRoleIcon(row.role)}
            label={getRoleLabel(row.role)}
            color={getRoleColor(row.role) as any}
            size="small"
            sx={{ fontWeight: 500 }}
          />
        );
      },
    },
    {
      field: 'created_at',
      headerName: 'تاریخ عضویت',
      minWidth: 150,
      renderCell: function render({ row }) {
        return (
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <DateIcon sx={{ fontSize: 14, color: 'text.secondary' }} />
            <Typography variant="body2">
              {new Date(row.created_at).toLocaleDateString('fa-IR')}
            </Typography>
          </Box>
        );
      },
    },
    {
      field: 'actions',
      headerName: 'عملیات',
      sortable: false,
      renderCell: function render({ row }) {
        return (
          <Stack direction="row" spacing={1}>
            <ShowButton size="small" recordItemId={row.id} />
            <EditButton size="small" recordItemId={row.id} />
            <DeleteButton size="small" recordItemId={row.id} />
          </Stack>
        );
      },
      align: 'center',
      headerAlign: 'center',
      minWidth: 180,
    },
  ];

  return (
    <>
      {/* Header Section */}
      <Box sx={{ mb: 3 }}>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mb: 2,
          }}
        >
          <Box>
            <Typography variant="h4" fontWeight={700} sx={{ mb: 0.5 }}>
              مدیریت کاربران
            </Typography>
            <Typography variant="body1" color="textSecondary">
              مدیریت کاربران سیستم، نقش‌ها و دسترسی‌ها
            </Typography>
          </Box>
          <Button
            variant="contained"
            size="large"
            startIcon={<Add />}
            onClick={() => setOpenCreateModal(true)}
            sx={{
              borderRadius: 3,
              px: 3,
              py: 1.5,
              background: 'linear-gradient(135deg, #7C3AED 0%, #A855F7 100%)',
              boxShadow: '0 4px 12px rgb(124 58 237 / 0.3)',
              '&:hover': {
                background: 'linear-gradient(135deg, #6D28D9 0%, #9333EA 100%)',
                boxShadow: '0 6px 16px rgb(124 58 237 / 0.4)',
                transform: 'translateY(-2px)',
              },
              transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
            }}
          >
            افزودن کاربر جدید
          </Button>
        </Box>

        {/* Statistics Cards */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ borderRadius: 3, border: '1px solid #E2E8F0' }}>
              <CardContent sx={{ textAlign: 'center', py: 2 }}>
                <Badge badgeContent={totalUsers} color="primary" max={999}>
                  <PersonIcon
                    sx={{ fontSize: 40, color: 'primary.main', mb: 1 }}
                  />
                </Badge>
                <Typography variant="h6" fontWeight={600}>
                  کل کاربران
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  تمام کاربران سیستم
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ borderRadius: 3, border: '1px solid #E2E8F0' }}>
              <CardContent sx={{ textAlign: 'center', py: 2 }}>
                <Badge badgeContent={adminCount} color="error" max={999}>
                  <AdminIcon
                    sx={{ fontSize: 40, color: 'error.main', mb: 1 }}
                  />
                </Badge>
                <Typography variant="h6" fontWeight={600}>
                  مدیران
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  مدیران سیستم
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ borderRadius: 3, border: '1px solid #E2E8F0' }}>
              <CardContent sx={{ textAlign: 'center', py: 2 }}>
                <Badge badgeContent={ownerCount} color="primary" max={999}>
                  <RestaurantOwnerIcon
                    sx={{ fontSize: 40, color: 'primary.main', mb: 1 }}
                  />
                </Badge>
                <Typography variant="h6" fontWeight={600}>
                  صاحبان رستوران
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  مالکان رستوران‌ها
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ borderRadius: 3, border: '1px solid #E2E8F0' }}>
              <CardContent sx={{ textAlign: 'center', py: 2 }}>
                <Badge badgeContent={customerCount} color="success" max={999}>
                  <CustomerIcon
                    sx={{ fontSize: 40, color: 'success.main', mb: 1 }}
                  />
                </Badge>
                <Typography variant="h6" fontWeight={600}>
                  مشتریان
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  کاربران عادی
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Filters Section */}
        <Paper
          sx={{ p: 2, borderRadius: 3, border: '1px solid #E2E8F0', mb: 3 }}
        >
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                size="small"
                placeholder="جستجو در نام، ایمیل یا شماره تلفن..."
                value={searchQuery}
                onChange={e => setSearchQuery(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <SearchIcon sx={{ color: 'text.secondary', mr: 1 }} />
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth size="small">
                <InputLabel>فیلتر بر اساس نقش</InputLabel>
                <Select
                  value={filterRole}
                  onChange={e => setFilterRole(e.target.value)}
                  label="فیلتر بر اساس نقش"
                >
                  <MenuItem value="">همه نقش‌ها</MenuItem>
                  <MenuItem value="admin">مدیر سیستم</MenuItem>
                  <MenuItem value="restaurant_owner">صاحب رستوران</MenuItem>
                  <MenuItem value="customer">مشتری</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={5}>
              <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
                <Tooltip title="بروزرسانی">
                  <IconButton
                    onClick={() =>
                      invalidate({ resource: 'users', invalidates: ['list'] })
                    }
                    sx={{ borderRadius: 2 }}
                  >
                    <RefreshIcon />
                  </IconButton>
                </Tooltip>
                <Tooltip title="خروجی Excel">
                  <IconButton sx={{ borderRadius: 2 }}>
                    <ExportIcon />
                  </IconButton>
                </Tooltip>
              </Box>
            </Grid>
          </Grid>
        </Paper>
      </Box>

      {/* Data Grid */}
      <Card
        sx={{
          borderRadius: 4,
          border: '1px solid #E2E8F0',
          boxShadow:
            '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
          overflow: 'hidden',
        }}
      >
        <CardContent sx={{ p: 0 }}>
          <DataGrid
            {...dataGridProps}
            columns={columns}
            autoHeight
            pageSizeOptions={[10, 25, 50, 100]}
            density="comfortable"
            sx={{
              border: 'none',
              '& .MuiDataGrid-cell': {
                borderBottom: '1px solid #F1F5F9',
                py: 2,
              },
              '& .MuiDataGrid-columnHeaders': {
                backgroundColor: '#F8FAFC',
                borderBottom: '1px solid #E2E8F0',
                '& .MuiDataGrid-columnHeader': {
                  fontWeight: 600,
                  fontSize: '0.875rem',
                },
              },
              '& .MuiDataGrid-row': {
                '&:hover': {
                  backgroundColor: '#F8FAFC',
                },
                '&.Mui-selected': {
                  backgroundColor: '#EDE9FE',
                  '&:hover': {
                    backgroundColor: '#DDD6FE',
                  },
                },
              },
              '& .MuiDataGrid-footerContainer': {
                borderTop: '1px solid #E2E8F0',
                backgroundColor: '#F8FAFC',
              },
            }}
          />
        </CardContent>
      </Card>

      {/* Create User Modal */}
      <Dialog
        open={openCreateModal}
        onClose={() => setOpenCreateModal(false)}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 4,
            boxShadow: '0 25px 50px -12px rgb(0 0 0 / 0.25)',
          },
        }}
      >
        <DialogTitle>
          <Box
            display="flex"
            justifyContent="space-between"
            alignItems="center"
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Avatar
                sx={{
                  bgcolor: 'primary.main',
                  background:
                    'linear-gradient(135deg, #7C3AED 0%, #A855F7 100%)',
                }}
              >
                <PersonIcon />
              </Avatar>
              <Box>
                <Typography variant="h6" fontWeight={600}>
                  افزودن کاربر جدید
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  اطلاعات کاربر جدید را وارد کنید
                </Typography>
              </Box>
            </Box>
            <IconButton
              onClick={() => setOpenCreateModal(false)}
              sx={{
                borderRadius: 2,
                '&:hover': { bgcolor: 'action.hover' },
              }}
            >
              <Close />
            </IconButton>
          </Box>
        </DialogTitle>

        <form
          onSubmit={handleSubmit(data =>
            handleCreateUser(data as UserFormData)
          )}
        >
          <DialogContent sx={{ px: 3 }}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <TextField
                  {...register('name', {
                    required: 'نام کاربر الزامی است',
                  })}
                  error={!!(errors as any)?.name}
                  helperText={(errors as any)?.name?.message}
                  margin="normal"
                  fullWidth
                  label="نام کامل"
                  name="name"
                  placeholder="نام و نام خانوادگی کاربر"
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  {...register('phone_number', {
                    required: 'شماره تلفن الزامی است',
                    pattern: {
                      value: /^[0-9+\-\s()]+$/,
                      message: 'شماره تلفن معتبر نیست',
                    },
                  })}
                  error={!!(errors as any)?.phone_number}
                  helperText={(errors as any)?.phone_number?.message}
                  margin="normal"
                  fullWidth
                  label="شماره تلفن"
                  name="phone_number"
                  placeholder="09123456789"
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  {...register('email', {
                    pattern: {
                      value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                      message: 'آدرس ایمیل معتبر نیست',
                    },
                  })}
                  error={!!(errors as any)?.email}
                  helperText={(errors as any)?.email?.message}
                  margin="normal"
                  fullWidth
                  type="email"
                  label="ایمیل (اختیاری)"
                  name="email"
                  placeholder="<EMAIL>"
                />
              </Grid>

              <Grid item xs={12}>
                <Controller
                  control={control}
                  name="role"
                  rules={{ required: 'انتخاب نقش الزامی است' }}
                  render={({ field }) => (
                    <FormControl
                      fullWidth
                      margin="normal"
                      error={!!(errors as any)?.role}
                    >
                      <InputLabel>نقش کاربر</InputLabel>
                      <Select {...field} label="نقش کاربر">
                        <MenuItem value="customer">
                          <Box
                            sx={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: 1,
                            }}
                          >
                            <CustomerIcon sx={{ fontSize: 20 }} />
                            مشتری
                          </Box>
                        </MenuItem>
                        <MenuItem value="restaurant_owner">
                          <Box
                            sx={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: 1,
                            }}
                          >
                            <RestaurantOwnerIcon sx={{ fontSize: 20 }} />
                            صاحب رستوران
                          </Box>
                        </MenuItem>
                        <MenuItem value="admin">
                          <Box
                            sx={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: 1,
                            }}
                          >
                            <AdminIcon sx={{ fontSize: 20 }} />
                            مدیر سیستم
                          </Box>
                        </MenuItem>
                      </Select>
                      {(errors as any)?.role && (
                        <Typography
                          variant="caption"
                          color="error"
                          sx={{ mt: 0.5, mx: 1.75 }}
                        >
                          {(errors as any)?.role?.message}
                        </Typography>
                      )}
                    </FormControl>
                  )}
                />
              </Grid>
            </Grid>
          </DialogContent>

          <DialogActions sx={{ px: 3, pb: 3 }}>
            <Button
              onClick={() => setOpenCreateModal(false)}
              variant="outlined"
              sx={{ borderRadius: 2 }}
            >
              انصراف
            </Button>
            <Button
              type="submit"
              variant="contained"
              sx={{
                borderRadius: 2,
                background: 'linear-gradient(135deg, #7C3AED 0%, #A855F7 100%)',
                px: 3,
              }}
            >
              ایجاد کاربر
            </Button>
          </DialogActions>
        </form>
      </Dialog>
    </>
  );
}
