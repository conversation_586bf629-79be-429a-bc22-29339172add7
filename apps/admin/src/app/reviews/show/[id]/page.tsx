'use client';

import React, { useState } from 'react';
import { useShow, useUpdate, useGo, useNotification } from '@refinedev/core';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Stack,
  Avatar,
  Rating,
  Chip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Divider,
  Alert,
  Grid,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  ArrowBack as BackIcon,
  Person as PersonIcon,
  Restaurant as RestaurantIcon,
  Flag as FlagIcon,
  CheckCircle as ApproveIcon,
  Block as BlockIcon,
  ThumbUp as ThumbUpIcon,
  ThumbDown as ThumbDownIcon,
  Report as ReportIcon,
  History as HistoryIcon,
  Edit as EditIcon,
} from '@mui/icons-material';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

interface ReviewShowPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default function ReviewShowPage({ params }: ReviewShowPageProps) {
  const [id, setId] = React.useState<string | null>(null);

  React.useEffect(() => {
    params.then(({ id }) => setId(id));
  }, [params]);
  const go = useGo();
  const { open } = useNotification();
  const { mutate: updateReview } = useUpdate();

  const [moderationDialog, setModerationDialog] = useState<{
    open: boolean;
    action?: 'approve' | 'reject' | 'flag';
    reason?: string;
  }>({ open: false });

  const { queryResult } = useShow({
    resource: 'reviews',
    id: id || '',
    meta: {
      populate: ['user', 'restaurant', 'reports', 'moderation_history'],
    },
  });

  const { data, isLoading, isError } = queryResult;
  const review = data?.data;

  const handleModerationAction = async (
    action: 'approve' | 'reject' | 'flag',
    reason?: string
  ) => {
    const statusMap = {
      approve: 'approved',
      reject: 'rejected',
      flag: 'flagged',
    };

    try {
      await updateReview({
        resource: 'reviews',
        id: id || '',
        values: {
          status: statusMap[action],
          ...(reason && { moderation_reason: reason }),
          moderated_at: new Date().toISOString(),
        },
      });

      open?.({
        type: 'success',
        message: `نظر با موفقیت ${action === 'approve' ? 'تایید' : action === 'reject' ? 'رد' : 'علامت‌گذاری'} شد`,
      });

      // Refresh the data
      queryResult.refetch();
    } catch (error) {
      open?.({
        type: 'error',
        message: 'خطا در انجام عملیات',
      });
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'success';
      case 'rejected':
        return 'error';
      case 'pending':
        return 'warning';
      case 'flagged':
        return 'secondary';
      default:
        return 'default';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'approved':
        return 'تایید شده';
      case 'rejected':
        return 'رد شده';
      case 'pending':
        return 'در انتظار';
      case 'flagged':
        return 'علامت‌گذاری شده';
      default:
        return status;
    }
  };

  if (!id || isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <Typography>در حال بارگذاری...</Typography>
      </Box>
    );
  }

  if (isError || !review) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <Alert severity="error">نظر مورد نظر یافت نشد</Alert>
      </Box>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Stack
        direction="row"
        justifyContent="space-between"
        alignItems="center"
        mb={3}
      >
        <Stack direction="row" alignItems="center" spacing={2}>
          <IconButton onClick={() => go({ to: '/reviews/list' })}>
            <BackIcon />
          </IconButton>
          <Typography variant="h4" component="h1">
            جزئیات نظر #{review.id?.toString().slice(-6)}
          </Typography>
          <Chip
            label={getStatusLabel(review.status)}
            color={getStatusColor(review.status) as any}
            size="medium"
          />
        </Stack>

        <Stack direction="row" spacing={1}>
          {review.status === 'pending' && (
            <>
              <Button
                variant="contained"
                color="success"
                startIcon={<ApproveIcon />}
                onClick={() => handleModerationAction('approve')}
              >
                تایید
              </Button>
              <Button
                variant="contained"
                color="error"
                startIcon={<BlockIcon />}
                onClick={() =>
                  setModerationDialog({ open: true, action: 'reject' })
                }
              >
                رد
              </Button>
            </>
          )}
          <Button
            variant="outlined"
            color="warning"
            startIcon={<FlagIcon />}
            onClick={() => setModerationDialog({ open: true, action: 'flag' })}
          >
            علامت‌گذاری
          </Button>
        </Stack>
      </Stack>

      <Grid container spacing={3}>
        {/* Main Review Content */}
        <Grid item xs={12} md={8}>
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Stack spacing={3}>
                {/* Review Header */}
                <Box
                  sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'flex-start',
                  }}
                >
                  <Box>
                    <Rating value={review.rating} readOnly size="large" />
                    <Typography variant="h6" sx={{ mt: 1 }}>
                      {review.title || 'نظر کاربر'}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {new Date(review.created_at).toLocaleDateString('fa-IR', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit',
                      })}
                    </Typography>
                  </Box>

                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Box
                      sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
                    >
                      <ThumbUpIcon fontSize="small" color="primary" />
                      <Typography variant="body2">
                        {review.helpful_count || 0}
                      </Typography>
                    </Box>
                    {review.is_reported && (
                      <Chip
                        icon={<ReportIcon />}
                        label={`${review.report_count || 1} گزارش`}
                        color="error"
                        size="small"
                      />
                    )}
                  </Box>
                </Box>

                <Divider />

                {/* Review Content */}
                <Box>
                  <Typography variant="body1" sx={{ lineHeight: 1.8 }}>
                    {review.comment || 'بدون متن نظر'}
                  </Typography>
                </Box>

                {/* Review Images */}
                {review.images && review.images.length > 0 && (
                  <Box>
                    <Typography variant="subtitle2" sx={{ mb: 1 }}>
                      تصاویر ضمیمه:
                    </Typography>
                    <Stack direction="row" spacing={1}>
                      {review.images.map((image: string, index: number) => (
                        <Box
                          key={index}
                          component="img"
                          src={image}
                          alt={`Review image ${index + 1}`}
                          sx={{
                            width: 100,
                            height: 100,
                            objectFit: 'cover',
                            borderRadius: 1,
                            cursor: 'pointer',
                          }}
                        />
                      ))}
                    </Stack>
                  </Box>
                )}

                {/* Moderation Info */}
                {review.moderation_reason && (
                  <Alert severity="info">
                    <Typography variant="subtitle2">دلیل اعمال شده:</Typography>
                    <Typography variant="body2">
                      {review.moderation_reason}
                    </Typography>
                  </Alert>
                )}
              </Stack>
            </CardContent>
          </Card>

          {/* Reports Section */}
          {review.reports && review.reports.length > 0 && (
            <Card>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  گزارش‌های دریافتی ({review.reports.length})
                </Typography>
                <List>
                  {review.reports.map((report: any, index: number) => (
                    <ListItem key={index} divider>
                      <ListItemIcon>
                        <ReportIcon color="error" />
                      </ListItemIcon>
                      <ListItemText
                        primary={report.reason || 'گزارش نامناسب بودن'}
                        secondary={
                          <Box>
                            <Typography variant="body2" color="text.secondary">
                              توسط: {report.reporter_name || 'کاربر ناشناس'}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              {new Date(report.created_at).toLocaleDateString(
                                'fa-IR'
                              )}
                            </Typography>
                          </Box>
                        }
                      />
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>
          )}
        </Grid>

        {/* Sidebar */}
        <Grid item xs={12} md={4}>
          {/* User Info */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                اطلاعات کاربر
              </Typography>
              <Stack spacing={2}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Avatar sx={{ width: 48, height: 48 }}>
                    {review.user?.avatar ? (
                      <img src={review.user.avatar} alt={review.user.name} />
                    ) : (
                      <PersonIcon />
                    )}
                  </Avatar>
                  <Box>
                    <Typography variant="subtitle1" fontWeight="medium">
                      {review.user?.name || 'کاربر ناشناس'}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {review.user?.email || ''}
                    </Typography>
                  </Box>
                </Box>

                <Stack
                  direction="row"
                  spacing={2}
                  sx={{ fontSize: '0.875rem' }}
                >
                  <Box>
                    <Typography variant="body2" color="text.secondary">
                      تعداد نظرات
                    </Typography>
                    <Typography variant="h6">
                      {review.user?.review_count || 0}
                    </Typography>
                  </Box>
                  <Box>
                    <Typography variant="body2" color="text.secondary">
                      میانگین امتیاز
                    </Typography>
                    <Typography variant="h6">
                      {review.user?.average_rating || 0}
                    </Typography>
                  </Box>
                </Stack>

                <Button
                  variant="outlined"
                  size="small"
                  onClick={() => go({ to: `/users/show/${review.user?.id}` })}
                >
                  مشاهده پروفایل
                </Button>
              </Stack>
            </CardContent>
          </Card>

          {/* Restaurant Info */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                اطلاعات رستوران
              </Typography>
              <Stack spacing={2}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Avatar sx={{ width: 48, height: 48 }}>
                    {review.restaurant?.logo ? (
                      <img
                        src={review.restaurant.logo}
                        alt={review.restaurant.name}
                      />
                    ) : (
                      <RestaurantIcon />
                    )}
                  </Avatar>
                  <Box>
                    <Typography variant="subtitle1" fontWeight="medium">
                      {review.restaurant?.name || 'رستوران ناشناس'}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {review.restaurant?.city || ''}
                    </Typography>
                  </Box>
                </Box>

                <Stack
                  direction="row"
                  spacing={2}
                  sx={{ fontSize: '0.875rem' }}
                >
                  <Box>
                    <Typography variant="body2" color="text.secondary">
                      تعداد نظرات
                    </Typography>
                    <Typography variant="h6">
                      {review.restaurant?.review_count || 0}
                    </Typography>
                  </Box>
                  <Box>
                    <Typography variant="body2" color="text.secondary">
                      میانگین امتیاز
                    </Typography>
                    <Typography variant="h6">
                      {review.restaurant?.average_rating || 0}
                    </Typography>
                  </Box>
                </Stack>

                <Button
                  variant="outlined"
                  size="small"
                  onClick={() =>
                    go({ to: `/restaurants/show/${review.restaurant?.id}` })
                  }
                >
                  مشاهده رستوران
                </Button>
              </Stack>
            </CardContent>
          </Card>

          {/* Moderation History */}
          {review.moderation_history &&
            review.moderation_history.length > 0 && (
              <Card>
                <CardContent>
                  <Typography variant="h6" sx={{ mb: 2 }}>
                    تاریخچه مدیریت
                  </Typography>
                  <List dense>
                    {review.moderation_history.map(
                      (history: any, index: number) => (
                        <ListItem key={index} divider>
                          <ListItemIcon>
                            <HistoryIcon fontSize="small" />
                          </ListItemIcon>
                          <ListItemText
                            primary={history.action}
                            secondary={
                              <Box>
                                <Typography
                                  variant="body2"
                                  color="text.secondary"
                                >
                                  توسط: {history.moderator_name}
                                </Typography>
                                <Typography
                                  variant="body2"
                                  color="text.secondary"
                                >
                                  {new Date(
                                    history.created_at
                                  ).toLocaleDateString('fa-IR')}
                                </Typography>
                              </Box>
                            }
                          />
                        </ListItem>
                      )
                    )}
                  </List>
                </CardContent>
              </Card>
            )}
        </Grid>
      </Grid>

      {/* Moderation Dialog */}
      <Dialog
        open={moderationDialog.open}
        onClose={() => setModerationDialog({ open: false })}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          {moderationDialog.action === 'reject' && 'رد نظر'}
          {moderationDialog.action === 'flag' && 'علامت‌گذاری نظر'}
        </DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            multiline
            rows={4}
            label="دلیل (اختیاری)"
            value={moderationDialog.reason || ''}
            onChange={e =>
              setModerationDialog(prev => ({ ...prev, reason: e.target.value }))
            }
            sx={{ mt: 2 }}
            placeholder="دلیل رد یا علامت‌گذاری این نظر را وارد کنید..."
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setModerationDialog({ open: false })}>
            انصراف
          </Button>
          <Button
            variant="contained"
            color={moderationDialog.action === 'reject' ? 'error' : 'warning'}
            onClick={() => {
              if (moderationDialog.action) {
                handleModerationAction(
                  moderationDialog.action,
                  moderationDialog.reason
                );
              }
              setModerationDialog({ open: false });
            }}
          >
            تایید
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}
