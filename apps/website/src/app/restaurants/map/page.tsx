'use client'

import { useState, useEffect, Suspense } from 'react'
import { useSearchParams } from 'next/navigation'
import Header from '@/components/Header'
import Footer from '@/components/Footer'
import RestaurantMap from '@/components/RestaurantMap'
import FilterSidebar from '@/components/FilterSidebar'
import { Restaurant } from '@/lib/api'
import apiService from '@/lib/api'
import { MapIcon, ListBulletIcon, AdjustmentsHorizontalIcon } from '@heroicons/react/24/outline'
import Link from 'next/link'

function RestaurantsMapContent() {
  const [restaurants, setRestaurants] = useState<Restaurant[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedRestaurant, setSelectedRestaurant] = useState<Restaurant | null>(null)
  const [showFilters, setShowFilters] = useState(false)
  const [filters, setFilters] = useState({
    search: '',
    category: '',
    minRating: 0,
    maxPrice: 0,
    sortBy: 'name' as 'name' | 'rating' | 'distance'
  })

  const searchParams = useSearchParams()

  useEffect(() => {
    const fetchRestaurants = async () => {
      try {
        setLoading(true)
        
        // Add filters to API call
        const searchParams: any = {}
        if (filters.search) searchParams.search = filters.search
        if (filters.category) searchParams.category = filters.category
        if (filters.minRating > 0) searchParams.min_rating = filters.minRating.toString()
        if (filters.maxPrice > 0) searchParams.max_price = filters.maxPrice.toString()
        searchParams.sort_by = filters.sortBy
        
        const response = await apiService.getRestaurants(searchParams)
        const data = Array.isArray(response) ? response : response.data
        setRestaurants(data)
      } catch (err) {
        console.error('Error fetching restaurants:', err)
        setError('خطا در بارگذاری رستوران‌ها')
      } finally {
        setLoading(false)
      }
    }

    fetchRestaurants()
  }, [filters])

  const handleFilterChange = (newFilters: any) => {
    setFilters(prev => ({ ...prev, ...newFilters }))
  }

  const handleRestaurantSelect = (restaurant: Restaurant) => {
    setSelectedRestaurant(restaurant)
  }

  const toggleFilters = () => {
    setShowFilters(!showFilters)
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <Header />
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-6"></div>
            <div className="h-96 bg-gray-200 dark:bg-gray-700 rounded"></div>
          </div>
        </main>
        <Footer />
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <Header />
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <p className="text-red-600 dark:text-red-400">{error}</p>
          </div>
        </main>
        <Footer />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
              نقشه رستوران‌ها
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              {restaurants.length} رستوران یافت شد
            </p>
          </div>
          
          {/* View Toggle */}
          <div className="flex items-center gap-4 mt-4 sm:mt-0">
            <div className="flex bg-white dark:bg-gray-800 rounded-lg p-1 shadow-sm">
              <Link
                href="/restaurants"
                className="flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
              >
                <ListBulletIcon className="w-4 h-4" />
                لیست
              </Link>
              <div className="flex items-center gap-2 px-3 py-2 text-sm font-medium bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300 rounded-md">
                <MapIcon className="w-4 h-4" />
                نقشه
              </div>
            </div>
            
            {/* Filter Toggle */}
            <button
              onClick={toggleFilters}
              className="flex items-center gap-2 px-4 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              <AdjustmentsHorizontalIcon className="w-4 h-4" />
              فیلترها
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex gap-6">
          {/* Filters Sidebar */}
          <div className={`${showFilters ? 'block' : 'hidden'} w-80 flex-shrink-0`}>
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                فیلترها
              </h2>
              
              {/* Search */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  جستجو
                </label>
                <input
                  type="text"
                  value={filters.search}
                  onChange={(e) => handleFilterChange({ search: e.target.value })}
                  placeholder="نام رستوران..."
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>

              {/* Rating Filter */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  حداقل امتیاز
                </label>
                <select
                  value={filters.minRating}
                  onChange={(e) => handleFilterChange({ minRating: Number(e.target.value) })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  <option value={0}>همه</option>
                  <option value={4}>4+ ستاره</option>
                  <option value={3}>3+ ستاره</option>
                  <option value={2}>2+ ستاره</option>
                </select>
              </div>

              {/* Sort */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  مرتب‌سازی
                </label>
                <select
                  value={filters.sortBy}
                  onChange={(e) => handleFilterChange({ sortBy: e.target.value as 'name' | 'rating' | 'distance' })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  <option value="name">نام</option>
                  <option value="rating">امتیاز</option>
                  <option value="distance">فاصله</option>
                </select>
              </div>

              {/* Clear Filters */}
              <button
                onClick={() => setFilters({
                  search: '',
                  category: '',
                  minRating: 0,
                  maxPrice: 0,
                  sortBy: 'name'
                })}
                className="w-full px-4 py-2 text-sm font-medium text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                پاک کردن فیلترها
              </button>
            </div>
          </div>

          {/* Map */}
          <div className="flex-1">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
              <RestaurantMap
                restaurants={restaurants}
                height="600px"
                onRestaurantSelect={handleRestaurantSelect}
              />
            </div>
          </div>
        </div>

        {/* Restaurant List Below Map (Mobile) */}
        <div className="mt-8 lg:hidden">
          <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
            رستوران‌های روی نقشه
          </h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            {restaurants.map((restaurant) => (
              <div
                key={restaurant.id}
                className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4 cursor-pointer transition-all ${
                  selectedRestaurant?.id === restaurant.id 
                    ? 'ring-2 ring-primary-500 border-primary-200' 
                    : 'hover:shadow-md'
                }`}
                onClick={() => setSelectedRestaurant(restaurant)}
              >
                <h3 className="font-semibold text-gray-900 dark:text-white mb-1">
                  {restaurant.name}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                  {restaurant.address}
                </p>
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <span className="text-sm font-medium text-gray-900 dark:text-white">
                                              {typeof restaurant.avg_rating === 'number' ? restaurant.avg_rating.toFixed(1) : Number(restaurant.avg_rating || 0).toFixed(1)} ⭐
                    </span>
                  </div>
                  <Link
                    href={`/restaurants/${restaurant.id}`}
                    className="text-primary-600 dark:text-primary-400 text-sm font-medium hover:underline"
                  >
                    مشاهده
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  )
}

export default function RestaurantsMapPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-6"></div>
            <div className="h-96 bg-gray-200 dark:bg-gray-700 rounded"></div>
          </div>
        </div>
      </div>
    }>
      <RestaurantsMapContent />
    </Suspense>
  )
} 