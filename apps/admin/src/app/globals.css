/* Import Vazirmatn font for Persian/Dari text */
@import url('https://cdn.jsdelivr.net/gh/rastikerdar/vazirmatn@v33.003/Vazirmatn-font-face.css');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Font family configuration for different languages */
:root {
  --font-vazirmatn:
    'Vazirmatn', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --font-english:
    'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Set RTL as default direction */
html {
  direction: rtl;
  font-family: var(--font-vazirmatn);
}

/* Apply Vazirmatn font for RTL languages (Persian/Dari and Arabic) */
[dir='rtl'],
body {
  font-family: var(--font-vazirmatn);
  direction: rtl;
}

/* Apply English font for LTR languages when explicitly set */
[dir='ltr'] {
  font-family: var(--font-english);
  direction: ltr;
}

/* Specific font for Persian/Dari content */
.font-vazirmatn,
[lang='fa'],
[lang='fa-IR'] {
  font-family: var(--font-vazirmatn) !important;
  direction: rtl;
}

/* English content styling */
[lang='en'],
[lang='en-US'] {
  font-family: var(--font-english) !important;
  direction: ltr;
}

/* Semi-dark theme variables */
:root {
  --background: #f7f7f9;
  --foreground: rgba(58, 53, 65, 0.87);
  --sidebar-bg: #30334e;
  --sidebar-text: #eaeaff;
  --primary-color: #666cff;
  --primary-light: #8589ff;
  --primary-dark: #5c61e6;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-vazirmatn);
}

/* Dark mode adjustments (if needed) */
@media (prefers-color-scheme: dark) {
  :root {
    --background: #282a42;
    --foreground: #eaeaff;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-vazirmatn);
  direction: rtl;
}

/* Ensure all text inputs and forms work properly with RTL */
input,
textarea,
select {
  direction: rtl;
  text-align: right;
}

/* Fix for MUI components RTL */
.MuiInputBase-input {
  text-align: right !important;
}

/* Ensure proper RTL for data tables */
.MuiDataGrid-root {
  direction: rtl;
}

.MuiDataGrid-cell {
  text-align: right;
}

/* Custom scrollbar for semi-dark theme */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(234, 234, 255, 0.08);
}

::-webkit-scrollbar-thumb {
  background: rgba(234, 234, 255, 0.22);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(234, 234, 255, 0.32);
}

/* Force 10px border radius for all cards and papers - Materialize theme */
.MuiCard-root,
.MuiPaper-root {
  border-radius: 10px !important;
}

/* Exception: drawers and app bars should have 0 border radius */
.MuiDrawer-paper,
.MuiDrawer-root .MuiPaper-root,
.MuiAppBar-root,
.MuiAppBar-root.MuiPaper-root {
  border-radius: 0px !important;
  border-top-left-radius: 0px !important;
  border-top-right-radius: 0px !important;
  border-bottom-left-radius: 0px !important;
  border-bottom-right-radius: 0px !important;
}

/* Override any inline border-radius styles */
[style*='border-radius: 24px'],
[style*='border-radius: 32px'] {
  border-radius: 10px !important;
}

/* Sidebar menu RTL alignment */
.MuiDrawer-paper .MuiList-root .MuiListItem-root {
  text-align: right !important;
  direction: rtl !important;
  justify-content: flex-end !important;
}

.MuiDrawer-paper .MuiList-root .MuiListItemText-root {
  text-align: right !important;
  direction: rtl !important;
}

.MuiDrawer-paper .MuiList-root .MuiListItemText-primary {
  text-align: right !important;
}

/* Table action buttons optimization */
.MuiDataGrid-cell .MuiStack-root .MuiIconButton-root {
  padding: 4px !important; /* Consistent reduced padding */
  border-radius: 4px !important;
  transition: all 0.2s ease !important;
}

/* Action column specific styling */
.MuiDataGrid-cell[data-field='actions'] {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Action button hover states */
.MuiDataGrid-row:hover .MuiIconButton-root {
  transform: scale(1.05) !important;
}

/* Ensure absolutely no border radius on sidebar and header */
[class*='MuiDrawer'],
[class*='MuiAppBar'],
.css-1vdcl8m-MuiPaper-root-MuiAppBar-root,
.css-1llubha-MuiDrawer-docked .MuiDrawer-paper {
  border-radius: 0px !important;
  border-top-left-radius: 0px !important;
  border-top-right-radius: 0px !important;
  border-bottom-left-radius: 0px !important;
  border-bottom-right-radius: 0px !important;
}
