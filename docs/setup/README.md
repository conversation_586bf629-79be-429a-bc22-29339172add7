# Setup Guide - Azkuja Restaurant Directory

This guide will help you set up the Azkuja Restaurant Directory with the new Next.js 15, React 19, pnpm, and Turbopack stack.

## 🔧 Prerequisites

### Required Software
- **Node.js 18.17.0+** - [Download](https://nodejs.org/)
- **pnpm 9.15.0+** - Package manager
- **PostgreSQL 13+** - Database
- **Redis** - Session storage (optional)

### System Requirements
- **macOS, Linux, or Windows** with WSL2
- **8GB RAM minimum** (16GB recommended)
- **10GB free disk space**

## 📦 Installation

### 1. Install pnpm
```bash
# Install pnpm globally
npm install -g pnpm@9.15.0

# Verify installation
pnpm --version
```

### 2. Clone and Setup Repository
```bash
# Clone the repository
git clone <repository-url>
cd Azkuja

# Install all dependencies
pnpm install
```

### 3. Environment Configuration

#### Backend Environment
Create `apps/backend/.env`:
```env
# Database
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_USERNAME=your_username
DATABASE_PASSWORD=your_password
DATABASE_NAME=azkuja_db

# JWT
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=30d

# Redis (optional)
REDIS_HOST=localhost
REDIS_PORT=6379

# Cloudinary (for image uploads)
CLOUDINARY_CLOUD_NAME=your_cloud_name
CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret

# Server
PORT=8000
NODE_ENV=development
```

#### Website Environment
Create `apps/website/.env.local`:
```env
# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_SOCKET_URL=http://localhost:8000

# Environment
NODE_ENV=development
```

### 4. Database Setup

#### PostgreSQL Setup
```bash
# Create database
createdb azkuja_db

# Run migrations (from backend directory)
cd apps/backend
pnpm run migration:run

# Seed test data (optional)
pnpm run seed
```

### 5. Start Development Servers

#### Option 1: Start All Services
```bash
# From root directory
pnpm dev:backend    # Terminal 1 - API server
pnpm dev:website    # Terminal 2 - Website with Turbopack
pnpm dev:admin      # Terminal 3 - Admin dashboard (optional)
```

#### Option 2: Start Individual Services
```bash
# Backend API (Port 8000)
pnpm --filter @azkuja/backend start:dev

# Website with Turbopack (Port 3000)
pnpm --filter @azkuja/website dev

# Admin Dashboard (Port 5050)
pnpm --filter @azkuja/admin dev
```

## 🚀 Verification

### 1. Check Services
- **Website**: http://localhost:3000
- **API**: http://localhost:8000/api (Swagger docs)
- **Admin**: http://localhost:5050

### 2. Test Authentication
1. Visit http://localhost:3000/test-auth
2. Login with phone: `0712345678`
3. Use OTP: `123456` (test environment)
4. Verify rapid refresh functionality

### 3. Verify Turbopack
Check terminal output for:
```
✓ Starting...
✓ Ready in <time>ms
○ Compiling /page ...
✓ Compiled /page in <time>ms
```

## 🔧 Troubleshooting

### Common Issues

#### 1. pnpm Installation Issues
```bash
# Clear pnpm cache
pnpm store prune

# Reinstall dependencies
rm -rf node_modules
pnpm install
```

#### 2. TypeScript Errors
```bash
# Clear Next.js cache
rm -rf apps/website/.next

# Restart TypeScript server in VS Code
Cmd/Ctrl + Shift + P → "TypeScript: Restart TS Server"
```

#### 3. Database Connection Issues
```bash
# Check PostgreSQL status
pg_ctl status

# Restart PostgreSQL
brew services restart postgresql  # macOS
sudo systemctl restart postgresql # Linux
```

#### 4. Port Conflicts
```bash
# Check what's using a port
lsof -i :3000
lsof -i :8000

# Kill process using port
kill -9 <PID>
```

### Performance Issues

#### Slow Builds
```bash
# Clear all caches
pnpm clean
rm -rf apps/website/.next
rm -rf apps/backend/dist

# Reinstall
pnpm install
```

#### Memory Issues
```bash
# Increase Node.js memory limit
export NODE_OPTIONS="--max-old-space-size=8192"
```

## 🔄 Development Workflow

### 1. Daily Development
```bash
# Start development
pnpm dev:backend
pnpm dev:website

# Make changes and test
# Turbopack will auto-reload
```

### 2. Adding Dependencies
```bash
# Add to website
pnpm --filter @azkuja/website add <package>

# Add to backend
pnpm --filter @azkuja/backend add <package>

# Add to workspace root
pnpm add -w <package>
```

### 3. Building for Production
```bash
# Build all
pnpm build:all

# Build specific app
pnpm build:website
pnpm build:backend
```

## 📱 Mobile App Setup (Optional)

### Prerequisites
- **React Native CLI**
- **Xcode** (iOS development)
- **Android Studio** (Android development)

### Setup
```bash
# Install React Native CLI
npm install -g @react-native-community/cli

# iOS setup
cd apps/mobile/ios
pod install

# Start Metro bundler
pnpm --filter @azkuja/mobile start

# Run on iOS
pnpm --filter @azkuja/mobile ios

# Run on Android
pnpm --filter @azkuja/mobile android
```

## 🎯 Next Steps

1. **Explore the codebase** - Familiarize yourself with the new structure
2. **Test authentication** - Ensure all auth flows work correctly
3. **Review API documentation** - Check [API docs](../api/)
4. **Read development guidelines** - See [Development guide](../development/)
5. **Start developing** - Begin working on new features

## 📞 Getting Help

If you encounter issues:
1. Check this troubleshooting section
2. Review the [Development Guide](../development/)
3. Check the project's issue tracker
4. Ask the development team

## 🔐 Security Notes

- Never commit `.env` files
- Use strong JWT secrets in production
- Keep dependencies updated
- Follow security best practices for authentication
