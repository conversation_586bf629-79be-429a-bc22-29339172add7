'use client';

import React, { useState } from 'react';
import {
  useDataGrid,
  EditButton,
  ShowButton,
  DeleteButton,
  useAutocomplete,
} from '@refinedev/mui';
import { useGo } from '@refinedev/core';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import {
  Box,
  Button,
  Card,
  CardContent,
  Stack,
  Typography,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  IconButton,
  Chip,
  Avatar,
  Tooltip,
  Badge,
  Paper,
  Autocomplete,
} from '@mui/material';
import { useCreate, useUpdate, useInvalidate } from '@refinedev/core';
import {
  Add,
  Close,
  Restaurant as RestaurantIcon,
  EventSeat as ReservationIcon,
  Person as CustomerIcon,
  CalendarToday as DateIcon,
  CheckCircle as ConfirmedIcon,
  Schedule as PendingIcon,
  Cancel as CanceledIcon,
  DoneAll as CompletedIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon,
  Today as TodayIcon,
  AccessTime as ClockIcon,
  Notes as NotesIcon,
} from '@mui/icons-material';
import { useForm } from '@refinedev/react-hook-form';
import { Controller } from 'react-hook-form';

// Force dynamic rendering to prevent SSG issues
export const dynamic = 'force-dynamic';

interface ReservationFormData {
  restaurant_id: string;
  party_size: number;
  reservation_date: string;
  reservation_time: string;
  special_requests?: string;
}

const RESERVATION_STATUS_CONFIG = {
  pending: {
    label: 'در انتظار تأیید',
    color: 'warning' as const,
    icon: <PendingIcon sx={{ fontSize: 16 }} />,
    bgColor: '#FFF3CD',
    textColor: '#856404',
  },
  confirmed: {
    label: 'تأیید شده',
    color: 'success' as const,
    icon: <ConfirmedIcon sx={{ fontSize: 16 }} />,
    bgColor: '#D4EDDA',
    textColor: '#155724',
  },
  canceled: {
    label: 'لغو شده',
    color: 'error' as const,
    icon: <CanceledIcon sx={{ fontSize: 16 }} />,
    bgColor: '#F8D7DA',
    textColor: '#721C24',
  },
  completed: {
    label: 'تکمیل شده',
    color: 'info' as const,
    icon: <CompletedIcon sx={{ fontSize: 16 }} />,
    bgColor: '#D1ECF1',
    textColor: '#0C5460',
  },
};

const TIME_SLOTS = [
  '11:00',
  '11:30',
  '12:00',
  '12:30',
  '13:00',
  '13:30',
  '14:00',
  '14:30',
  '15:00',
  '15:30',
  '16:00',
  '16:30',
  '17:00',
  '17:30',
  '18:00',
  '18:30',
  '19:00',
  '19:30',
  '20:00',
  '20:30',
  '21:00',
  '21:30',
  '22:00',
  '22:30',
];

export default function ReservationsList() {
  const go = useGo();
  const invalidate = useInvalidate();
  const [openCreateModal, setOpenCreateModal] = useState(false);
  const [openStatusModal, setOpenStatusModal] = useState(false);
  const [selectedReservation, setSelectedReservation] = useState<any>(null);
  const [filterStatus, setFilterStatus] = useState('');
  const [filterDate, setFilterDate] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const { mutate: createReservation } = useCreate();
  const { mutate: updateReservation } = useUpdate();

  const { dataGridProps } = useDataGrid({
    resource: 'reservations',
    syncWithLocation: true,
    pagination: {
      mode: 'server',
    },
    filters: {
      mode: 'server',
      initial: [
        ...(filterStatus
          ? [
              {
                field: 'status',
                operator: 'eq' as const,
                value: filterStatus,
              },
            ]
          : []),
        ...(filterDate
          ? [
              {
                field: 'reservation_date',
                operator: 'eq' as const,
                value: filterDate,
              },
            ]
          : []),
        ...(searchQuery
          ? [
              {
                field: 'search',
                operator: 'contains' as const,
                value: searchQuery,
              },
            ]
          : []),
      ],
    },
    sorters: {
      mode: 'server',
      initial: [
        {
          field: 'reservation_date',
          order: 'desc',
        },
        {
          field: 'reservation_time',
          order: 'desc',
        },
      ],
    },
  });

  // Form for creating reservation
  const {
    register,
    control,
    formState: { errors },
    handleSubmit,
    reset,
  } = useForm<ReservationFormData>({
    defaultValues: {
      restaurant_id: '',
      party_size: 1,
      reservation_date: '',
      reservation_time: '',
      special_requests: '',
    },
  });

  // Autocomplete for restaurants
  const { autocompleteProps: restaurantAutocompleteProps } = useAutocomplete({
    resource: 'restaurants',
    onSearch: value => [
      {
        field: 'name',
        operator: 'contains',
        value,
      },
    ],
  });

  const handleCreateReservation = (data: any) => {
    try {
      console.log('Creating reservation with data:', data);

      if (
        !data.restaurant_id ||
        !data.party_size ||
        !data.reservation_date ||
        !data.reservation_time
      ) {
        alert('لطفاً تمام فیلدهای الزامی را پر کنید');
        return;
      }

      const cleanData = {
        restaurant_id: data.restaurant_id,
        party_size: Number(data.party_size),
        reservation_date: data.reservation_date,
        reservation_time: data.reservation_time,
        special_requests: data.special_requests?.trim() || undefined,
      };

      createReservation(
        {
          resource: 'reservations',
          values: cleanData,
        },
        {
          onSuccess: response => {
            console.log('Reservation created successfully:', response);
            setOpenCreateModal(false);
            reset();
            invalidate({
              resource: 'reservations',
              invalidates: ['list'],
            });
          },
          onError: error => {
            console.error('Error creating reservation:', error);
            alert('خطا در ایجاد رزرو: ' + error.message);
          },
        }
      );
    } catch (error) {
      console.error('Error in handleCreateReservation:', error);
    }
  };

  const handleStatusUpdate = (newStatus: string) => {
    if (!selectedReservation) return;

    updateReservation(
      {
        resource: 'reservations',
        id: selectedReservation.id,
        values: { status: newStatus },
      },
      {
        onSuccess: () => {
          setOpenStatusModal(false);
          setSelectedReservation(null);
          invalidate({
            resource: 'reservations',
            invalidates: ['list'],
          });
        },
        onError: error => {
          console.error('Error updating reservation status:', error);
          alert('خطا در بروزرسانی وضعیت رزرو');
        },
      }
    );
  };

  const getStatusDisplay = (status: string) => {
    return (
      RESERVATION_STATUS_CONFIG[
        status as keyof typeof RESERVATION_STATUS_CONFIG
      ] || RESERVATION_STATUS_CONFIG.pending
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fa-IR');
  };

  const getPartySizeIcon = (partySize: number) => {
    if (partySize <= 2) return '👫';
    if (partySize <= 4) return '👨‍👩‍👧‍👦';
    if (partySize <= 6) return '👥';
    return '🎉';
  };

  // Calculate reservation statistics
  const totalReservations = dataGridProps?.rowCount || 0;
  const pendingReservations =
    dataGridProps?.rows?.filter((item: any) => item.status === 'pending')
      .length || 0;
  const confirmedReservations =
    dataGridProps?.rows?.filter((item: any) => item.status === 'confirmed')
      .length || 0;
  const todayReservations =
    dataGridProps?.rows?.filter((item: any) => {
      const today = new Date().toISOString().split('T')[0];
      return item.reservation_date === today;
    }).length || 0;

  const columns: GridColDef[] = [
    {
      field: 'id',
      headerName: 'شناسه رزرو',
      minWidth: 150,
      renderCell: ({ row }) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Avatar
            sx={{
              bgcolor: 'primary.main',
              width: 32,
              height: 32,
              fontSize: '0.8rem',
            }}
          >
            <ReservationIcon sx={{ fontSize: 16 }} />
          </Avatar>
          <Box>
            <Typography variant="subtitle2" fontWeight={600}>
              #{row.id.slice(-8)}
            </Typography>
            <Typography variant="caption" color="textSecondary">
              {new Date(row.created_at).toLocaleDateString('fa-IR')}
            </Typography>
          </Box>
        </Box>
      ),
    },
    {
      field: 'restaurant',
      headerName: 'رستوران',
      minWidth: 200,
      renderCell: ({ row }) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <RestaurantIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
          <Typography variant="body2">
            {row.restaurant?.name || 'نامشخص'}
          </Typography>
        </Box>
      ),
    },
    {
      field: 'user',
      headerName: 'مشتری',
      minWidth: 180,
      renderCell: ({ row }) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <CustomerIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
          <Box>
            <Typography variant="body2" fontWeight={500}>
              {row.user?.name || 'نامشخص'}
            </Typography>
            <Typography variant="caption" color="textSecondary">
              {row.user?.phone_number}
            </Typography>
          </Box>
        </Box>
      ),
    },
    {
      field: 'party_size',
      headerName: 'تعداد نفرات',
      minWidth: 120,
      renderCell: ({ row }) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <span style={{ fontSize: '1.2rem' }}>
            {getPartySizeIcon(row.party_size)}
          </span>
          <Typography variant="body2" fontWeight={600}>
            {row.party_size} نفر
          </Typography>
        </Box>
      ),
    },
    {
      field: 'reservation_date',
      headerName: 'تاریخ رزرو',
      minWidth: 130,
      renderCell: ({ row }) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <DateIcon sx={{ fontSize: 16, color: 'primary.main' }} />
          <Typography variant="body2">
            {formatDate(row.reservation_date)}
          </Typography>
        </Box>
      ),
    },
    {
      field: 'reservation_time',
      headerName: 'زمان رزرو',
      minWidth: 120,
      renderCell: ({ row }) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <ClockIcon sx={{ fontSize: 16, color: 'secondary.main' }} />
          <Typography variant="body2" fontWeight={600}>
            {row.reservation_time}
          </Typography>
        </Box>
      ),
    },
    {
      field: 'status',
      headerName: 'وضعیت',
      minWidth: 150,
      renderCell: ({ row }) => {
        const statusConfig = getStatusDisplay(row.status);
        return (
          <Chip
            icon={statusConfig.icon}
            label={statusConfig.label}
            size="small"
            sx={{
              fontWeight: 500,
              backgroundColor: statusConfig.bgColor,
              color: statusConfig.textColor,
              border: 'none',
              cursor: 'pointer',
            }}
            onClick={() => {
              setSelectedReservation(row);
              setOpenStatusModal(true);
            }}
          />
        );
      },
    },
    {
      field: 'special_requests',
      headerName: 'درخواست‌های ویژه',
      minWidth: 200,
      renderCell: ({ row }) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {row.special_requests ? (
            <>
              <NotesIcon sx={{ fontSize: 16, color: 'info.main' }} />
              <Typography
                variant="body2"
                sx={{
                  maxWidth: 150,
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                }}
              >
                {row.special_requests}
              </Typography>
            </>
          ) : (
            <Typography variant="body2" color="textSecondary">
              ندارد
            </Typography>
          )}
        </Box>
      ),
    },
    {
      field: 'actions',
      headerName: 'عملیات',
      sortable: false,
      renderCell: function render({ row }) {
        return (
          <Button
            size="small"
            onClick={() => go({ to: `/reservations/show/${row.id}` })}
          >
            مشاهده
          </Button>
        );
      },
      align: 'center',
      headerAlign: 'center',
      minWidth: 120,
    },
  ];

  return (
    <>
      {/* Header Section */}
      <Box sx={{ mb: 3 }}>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mb: 2,
          }}
        >
          <Box>
            <Typography variant="h4" fontWeight={700} sx={{ mb: 0.5 }}>
              مدیریت رزروها
            </Typography>
            <Typography variant="body1" color="textSecondary">
              مدیریت و پیگیری رزرو میزهای رستوران
            </Typography>
          </Box>
          <Button
            variant="contained"
            size="large"
            startIcon={<Add />}
            onClick={() => setOpenCreateModal(true)}
            sx={{
              borderRadius: 3,
              px: 3,
              py: 1.5,
              background: 'linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%)',
              boxShadow: '0 4px 12px rgb(46 125 50 / 0.3)',
              '&:hover': {
                background: 'linear-gradient(135deg, #1B5E20 0%, #388E3C 100%)',
                boxShadow: '0 6px 16px rgb(46 125 50 / 0.4)',
                transform: 'translateY(-2px)',
              },
              transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
            }}
          >
            ایجاد رزرو جدید
          </Button>
        </Box>

        {/* Statistics Cards */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ borderRadius: 3, border: '1px solid #E2E8F0' }}>
              <CardContent sx={{ textAlign: 'center', py: 2 }}>
                <Badge
                  badgeContent={totalReservations}
                  color="primary"
                  max={999}
                >
                  <ReservationIcon
                    sx={{ fontSize: 40, color: 'primary.main', mb: 1 }}
                  />
                </Badge>
                <Typography variant="h6" fontWeight={600}>
                  کل رزروها
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  تمام رزروها
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ borderRadius: 3, border: '1px solid #E2E8F0' }}>
              <CardContent sx={{ textAlign: 'center', py: 2 }}>
                <Badge
                  badgeContent={pendingReservations}
                  color="warning"
                  max={999}
                >
                  <PendingIcon
                    sx={{ fontSize: 40, color: 'warning.main', mb: 1 }}
                  />
                </Badge>
                <Typography variant="h6" fontWeight={600}>
                  در انتظار
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  نیاز به تأیید
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ borderRadius: 3, border: '1px solid #E2E8F0' }}>
              <CardContent sx={{ textAlign: 'center', py: 2 }}>
                <Badge
                  badgeContent={confirmedReservations}
                  color="success"
                  max={999}
                >
                  <ConfirmedIcon
                    sx={{ fontSize: 40, color: 'success.main', mb: 1 }}
                  />
                </Badge>
                <Typography variant="h6" fontWeight={600}>
                  تأیید شده
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  آماده پذیرایی
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ borderRadius: 3, border: '1px solid #E2E8F0' }}>
              <CardContent sx={{ textAlign: 'center', py: 2 }}>
                <Badge badgeContent={todayReservations} color="info" max={999}>
                  <TodayIcon sx={{ fontSize: 40, color: 'info.main', mb: 1 }} />
                </Badge>
                <Typography variant="h6" fontWeight={600}>
                  امروز
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  رزروهای امروز
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Filters Section */}
        <Paper
          sx={{ p: 2, borderRadius: 3, border: '1px solid #E2E8F0', mb: 3 }}
        >
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                size="small"
                placeholder="جستجو در نام مشتری..."
                value={searchQuery}
                onChange={e => setSearchQuery(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <SearchIcon sx={{ color: 'text.secondary', mr: 1 }} />
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth size="small">
                <InputLabel>فیلتر وضعیت</InputLabel>
                <Select
                  value={filterStatus}
                  onChange={e => setFilterStatus(e.target.value)}
                  label="فیلتر وضعیت"
                >
                  <MenuItem value="">همه وضعیت‌ها</MenuItem>
                  {Object.entries(RESERVATION_STATUS_CONFIG).map(
                    ([key, config]) => (
                      <MenuItem key={key} value={key}>
                        {config.icon} {config.label}
                      </MenuItem>
                    )
                  )}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                size="small"
                type="date"
                label="فیلتر تاریخ"
                value={filterDate}
                onChange={e => setFilterDate(e.target.value)}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} md={2}>
              <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
                <Tooltip title="بروزرسانی">
                  <IconButton
                    onClick={() =>
                      invalidate({
                        resource: 'reservations',
                        invalidates: ['list'],
                      })
                    }
                    sx={{ borderRadius: 2 }}
                  >
                    <RefreshIcon />
                  </IconButton>
                </Tooltip>
              </Box>
            </Grid>
          </Grid>
        </Paper>
      </Box>

      {/* Data Grid */}
      <Card
        sx={{
          borderRadius: 4,
          border: '1px solid #E2E8F0',
          boxShadow:
            '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
          overflow: 'hidden',
        }}
      >
        <CardContent sx={{ p: 0 }}>
          <DataGrid
            {...dataGridProps}
            columns={columns}
            autoHeight
            pageSizeOptions={[10, 25, 50, 100]}
            density="comfortable"
            sx={{
              border: 'none',
              '& .MuiDataGrid-cell': {
                borderBottom: '1px solid #F1F5F9',
                py: 2,
              },
              '& .MuiDataGrid-columnHeaders': {
                backgroundColor: '#F8FAFC',
                borderBottom: '1px solid #E2E8F0',
                '& .MuiDataGrid-columnHeader': {
                  fontWeight: 600,
                  fontSize: '0.875rem',
                },
              },
              '& .MuiDataGrid-row': {
                '&:hover': {
                  backgroundColor: '#F8FAFC',
                },
                '&.Mui-selected': {
                  backgroundColor: '#E8F5E8',
                  '&:hover': {
                    backgroundColor: '#C8E6C9',
                  },
                },
              },
              '& .MuiDataGrid-footerContainer': {
                borderTop: '1px solid #E2E8F0',
                backgroundColor: '#F8FAFC',
              },
            }}
          />
        </CardContent>
      </Card>

      {/* Create Reservation Modal */}
      <Dialog
        open={openCreateModal}
        onClose={() => setOpenCreateModal(false)}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 4,
            boxShadow: '0 25px 50px -12px rgb(0 0 0 / 0.25)',
          },
        }}
      >
        <DialogTitle>
          <Box
            display="flex"
            justifyContent="space-between"
            alignItems="center"
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Avatar
                sx={{
                  bgcolor: 'primary.main',
                  background:
                    'linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%)',
                }}
              >
                <ReservationIcon />
              </Avatar>
              <Box>
                <Typography variant="h6" fontWeight={600}>
                  ایجاد رزرو جدید
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  اطلاعات رزرو میز را وارد کنید
                </Typography>
              </Box>
            </Box>
            <IconButton
              onClick={() => setOpenCreateModal(false)}
              sx={{
                borderRadius: 2,
                '&:hover': { bgcolor: 'action.hover' },
              }}
            >
              <Close />
            </IconButton>
          </Box>
        </DialogTitle>

        <form onSubmit={handleSubmit(handleCreateReservation)}>
          <DialogContent sx={{ px: 3 }}>
            <Grid container spacing={3}>
              {/* Restaurant Selection */}
              <Grid item xs={12}>
                <Typography
                  variant="h6"
                  gutterBottom
                  sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}
                >
                  🏪 انتخاب رستوران
                </Typography>
                <Controller
                  control={control}
                  name="restaurant_id"
                  rules={{ required: 'انتخاب رستوران الزامی است' }}
                  render={({ field }) => (
                    <Autocomplete
                      {...restaurantAutocompleteProps}
                      {...field}
                      onChange={(_, value) => field.onChange(value?.id)}
                      getOptionLabel={item => item.name || ''}
                      isOptionEqualToValue={(option, value) =>
                        value === undefined ||
                        option?.id?.toString() === value?.id?.toString()
                      }
                      renderInput={params => (
                        <TextField
                          {...params}
                          label="رستوران"
                          margin="normal"
                          variant="outlined"
                          error={!!(errors as any)?.restaurant_id}
                          helperText={(errors as any)?.restaurant_id?.message}
                          required
                        />
                      )}
                    />
                  )}
                />
              </Grid>

              {/* Reservation Details */}
              <Grid item xs={12}>
                <Typography
                  variant="h6"
                  gutterBottom
                  sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}
                >
                  📅 جزئیات رزرو
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <TextField
                      {...register('party_size', {
                        required: 'تعداد نفرات الزامی است',
                        min: {
                          value: 1,
                          message: 'تعداد نفرات باید حداقل 1 باشد',
                        },
                        max: {
                          value: 20,
                          message: 'تعداد نفرات نمی‌تواند بیش از 20 باشد',
                        },
                      })}
                      error={!!(errors as any)?.party_size}
                      helperText={(errors as any)?.party_size?.message}
                      margin="normal"
                      fullWidth
                      type="number"
                      label="تعداد نفرات"
                      name="party_size"
                      placeholder="مثال: 4"
                      inputProps={{ min: 1, max: 20 }}
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <TextField
                      {...register('reservation_date', {
                        required: 'تاریخ رزرو الزامی است',
                      })}
                      error={!!(errors as any)?.reservation_date}
                      helperText={(errors as any)?.reservation_date?.message}
                      margin="normal"
                      fullWidth
                      type="date"
                      label="تاریخ رزرو"
                      name="reservation_date"
                      InputLabelProps={{ shrink: true }}
                      inputProps={{
                        min: new Date().toISOString().split('T')[0], // Prevent past dates
                      }}
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Controller
                      control={control}
                      name="reservation_time"
                      rules={{ required: 'زمان رزرو الزامی است' }}
                      render={({ field }) => (
                        <FormControl
                          fullWidth
                          margin="normal"
                          error={!!(errors as any)?.reservation_time}
                        >
                          <InputLabel>زمان رزرو</InputLabel>
                          <Select {...field} label="زمان رزرو">
                            {TIME_SLOTS.map(time => (
                              <MenuItem key={time} value={time}>
                                {time}
                              </MenuItem>
                            ))}
                          </Select>
                          {(errors as any)?.reservation_time && (
                            <Typography
                              variant="caption"
                              color="error"
                              sx={{ mt: 1, mx: 2 }}
                            >
                              {(errors as any)?.reservation_time?.message}
                            </Typography>
                          )}
                        </FormControl>
                      )}
                    />
                  </Grid>
                </Grid>
              </Grid>

              {/* Special Requests */}
              <Grid item xs={12}>
                <Typography
                  variant="h6"
                  gutterBottom
                  sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}
                >
                  📝 درخواست‌های ویژه (اختیاری)
                </Typography>
                <TextField
                  {...register('special_requests')}
                  margin="normal"
                  fullWidth
                  multiline
                  rows={3}
                  label="درخواست‌های ویژه"
                  name="special_requests"
                  placeholder="مثال: میز کنار پنجره، صندلی کودک، جشن تولد..."
                />
              </Grid>
            </Grid>
          </DialogContent>

          <DialogActions sx={{ px: 3, pb: 3 }}>
            <Button
              onClick={() => setOpenCreateModal(false)}
              variant="outlined"
              sx={{ borderRadius: 2 }}
            >
              انصراف
            </Button>
            <Button
              type="submit"
              variant="contained"
              sx={{
                borderRadius: 2,
                background: 'linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%)',
                px: 3,
              }}
            >
              ایجاد رزرو
            </Button>
          </DialogActions>
        </form>
      </Dialog>

      {/* Status Update Modal */}
      <Dialog
        open={openStatusModal}
        onClose={() => setOpenStatusModal(false)}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 4,
            boxShadow: '0 25px 50px -12px rgb(0 0 0 / 0.25)',
          },
        }}
      >
        <DialogTitle>
          <Box
            display="flex"
            justifyContent="space-between"
            alignItems="center"
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Avatar
                sx={{
                  bgcolor: 'primary.main',
                  background:
                    'linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%)',
                }}
              >
                <ReservationIcon />
              </Avatar>
              <Box>
                <Typography variant="h6" fontWeight={600}>
                  بروزرسانی وضعیت رزرو
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  رزرو #{selectedReservation?.id?.slice(-8)}
                </Typography>
              </Box>
            </Box>
            <IconButton
              onClick={() => setOpenStatusModal(false)}
              sx={{
                borderRadius: 2,
                '&:hover': { bgcolor: 'action.hover' },
              }}
            >
              <Close />
            </IconButton>
          </Box>
        </DialogTitle>

        <DialogContent sx={{ px: 3 }}>
          <Grid container spacing={2}>
            {Object.entries(RESERVATION_STATUS_CONFIG).map(
              ([status, config]) => (
                <Grid item xs={12} sm={6} key={status}>
                  <Button
                    fullWidth
                    variant={
                      selectedReservation?.status === status
                        ? 'contained'
                        : 'outlined'
                    }
                    startIcon={config.icon}
                    onClick={() => handleStatusUpdate(status)}
                    sx={{
                      py: 2,
                      borderRadius: 2,
                      justifyContent: 'flex-start',
                      textTransform: 'none',
                      ...(selectedReservation?.status === status && {
                        background: config.bgColor,
                        color: config.textColor,
                        borderColor: config.textColor,
                      }),
                    }}
                  >
                    {config.label}
                  </Button>
                </Grid>
              )
            )}
          </Grid>
        </DialogContent>

        <DialogActions sx={{ px: 3, pb: 3 }}>
          <Button
            onClick={() => setOpenStatusModal(false)}
            variant="outlined"
            sx={{ borderRadius: 2 }}
          >
            انصراف
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}
