#!/bin/bash

# Turborepo Migration Validation Script
# This script validates that the Turborepo migration was successful

set -e

echo "🚀 Validating Turborepo Migration for Azkuja"
echo "=============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print status
print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
        exit 1
    fi
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

echo ""
echo "1. Checking Turborepo Installation..."
pnpm turbo --version > /dev/null 2>&1
print_status $? "Turborepo is installed and accessible"

echo ""
echo "2. Validating turbo.json configuration..."
if [ -f "turbo.json" ]; then
    print_status 0 "turbo.json exists"
    
    # Check if turbo.json has required tasks
    if grep -q '"build"' turbo.json && grep -q '"dev"' turbo.json && grep -q '"lint"' turbo.json; then
        print_status 0 "turbo.json contains required tasks (build, dev, lint)"
    else
        print_status 1 "turbo.json missing required tasks"
    fi
else
    print_status 1 "turbo.json not found"
fi

echo ""
echo "3. Checking workspace configuration..."
if [ -f "pnpm-workspace.yaml" ]; then
    print_status 0 "pnpm-workspace.yaml exists"
else
    print_status 1 "pnpm-workspace.yaml not found"
fi

echo ""
echo "4. Validating package scripts..."
packages=("@azkuja/shared" "@azkuja/website" "@azkuja/backend" "@azkuja/admin")

for package in "${packages[@]}"; do
    echo "   Checking $package..."
    
    # Check if package has required scripts
    if pnpm --filter "$package" run --help > /dev/null 2>&1; then
        print_status 0 "$package is accessible via pnpm filter"
    else
        print_status 1 "$package not accessible via pnpm filter"
    fi
done

echo ""
echo "5. Testing Turborepo tasks..."

echo "   Testing shared package build..."
pnpm turbo build --filter=@azkuja/shared > /dev/null 2>&1
print_status $? "Shared package builds successfully"

echo "   Testing lint task..."
pnpm turbo lint --filter=@azkuja/shared > /dev/null 2>&1
print_status $? "Lint task runs successfully"

echo "   Testing type-check task..."
pnpm turbo type-check --filter=@azkuja/shared > /dev/null 2>&1
print_status $? "Type-check task runs successfully"

echo ""
echo "6. Checking caching..."
echo "   Running build twice to test caching..."

# First build (cache miss)
pnpm turbo build --filter=@azkuja/shared > /tmp/turbo_build_1.log 2>&1
if grep -q "cache miss" /tmp/turbo_build_1.log; then
    print_status 0 "First build shows cache miss (expected)"
else
    print_warning "First build didn't show cache miss status"
fi

# Second build (should be cached)
pnpm turbo build --filter=@azkuja/shared > /tmp/turbo_build_2.log 2>&1
if grep -q "cache hit" /tmp/turbo_build_2.log; then
    print_status 0 "Second build shows cache hit (caching works!)"
else
    print_warning "Second build didn't show cache hit - caching may not be working optimally"
fi

echo ""
echo "7. Validating directory structure..."
required_dirs=("apps/website" "apps/backend" "apps/admin" "packages/shared" "docs")

for dir in "${required_dirs[@]}"; do
    if [ -d "$dir" ]; then
        print_status 0 "$dir directory exists"
    else
        print_status 1 "$dir directory missing"
    fi
done

echo ""
echo "8. Checking configuration files..."
config_files=(".eslintrc.js" ".prettierrc" ".prettierignore" "turbo.json")

for file in "${config_files[@]}"; do
    if [ -f "$file" ]; then
        print_status 0 "$file exists"
    else
        print_status 1 "$file missing"
    fi
done

echo ""
echo "9. Testing parallel execution..."
echo "   This test checks if Turborepo can handle multiple tasks..."

# Test parallel lint execution
pnpm turbo lint --parallel > /tmp/turbo_parallel.log 2>&1
if [ $? -eq 0 ]; then
    print_status 0 "Parallel task execution works"
else
    print_warning "Parallel task execution had issues (check logs)"
fi

echo ""
echo "10. Final validation..."
echo "    Checking if all core functionality is preserved..."

# Check if package.json has turbo scripts
if grep -q "turbo" package.json; then
    print_status 0 "Root package.json contains Turborepo scripts"
else
    print_status 1 "Root package.json missing Turborepo scripts"
fi

echo ""
echo "🎉 Turborepo Migration Validation Complete!"
echo "=============================================="
echo ""
echo "Next steps:"
echo "1. Test the authentication system at http://localhost:3000/test-auth"
echo "2. Run 'pnpm dev' to start all development servers"
echo "3. Run 'pnpm build' to build all packages"
echo "4. Check the docs/ folder for updated documentation"
echo ""
echo "For more information, see:"
echo "- docs/development/README.md for development workflows"
echo "- docs/setup/README.md for setup instructions"
echo ""
