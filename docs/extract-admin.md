# Extracting Admin Panel to Separate Repository

## Step 1: Create a new repository
Create a new repository in GitHub/GitLab/etc. named "azkuja-admin"

## Step 2: Copy the admin package files
1. Copy the entire contents of `packages/admin` to a new local directory
2. Create a new package.json for the standalone admin repository:

```json
{
  "name": "azkuja-admin",
  "version": "0.1.0",
  "private": true,
  "scripts": {
    "dev": "next dev -p 3002",
    "build": "next build",
    "start": "next start -p 3002",
    "lint": "next lint"
  },
  "dependencies": {
    "@emotion/react": "^11.11.1",
    "@emotion/styled": "^11.11.0",
    "@refinedev/core": "^4.45.1",
    "@refinedev/inferencer": "^4.5.21",
    "@refinedev/kbar": "^1.3.0",
    "@refinedev/mui": "^5.13.21",
    "@refinedev/nextjs-router": "^5.5.5",
    "@refinedev/react-hook-form": "^4.8.11",
    "@refinedev/react-table": "^5.6.5",
    "@mui/icons-material": "^5.14.19",
    "@mui/lab": "^5.0.0-alpha.155",
    "@mui/material": "^5.14.19",
    "@mui/x-data-grid": "^6.18.2",
    "axios": "^1.6.2",
    "next": "14.1.0",
    "nookies": "^2.5.2",
    "react": "^18",
    "react-dom": "^18",
    "react-hook-form": "^7.48.2"
  },
  "devDependencies": {
    "@types/node": "^20",
    "@types/react": "^18",
    "@types/react-dom": "^18",
    "autoprefixer": "^10.0.1",
    "eslint": "^8",
    "eslint-config-next": "14.1.0",
    "postcss": "^8",
    "tailwindcss": "^3.3.0",
    "typescript": "^5"
  }
}
```

## Step 3: Copy the shared types
Copy the types you need from the `packages/shared/src/types` directory into a new `src/types` directory in your admin project.

## Step 4: Update imports
Update all imports from `@azkuja/shared` to the local types:

```typescript
// Before
import { Restaurant, Category } from '@azkuja/shared';

// After
import { Restaurant, Category } from '@/types';
```

## Step 5: Update API configuration
Update the API URL in your API service to point to your deployed backend:

```typescript
// Example: src/api/axiosInstance.ts or similar
const API_URL = 'https://your-backend-url.com/api';
```

## Step 6: Initialize local Git repository
```bash
git init
git add .
git commit -m "Initial commit"
git remote add origin https://github.com/yourusername/azkuja-admin.git
git push -u origin main
``` 