# 🎉 Ngrok Setup Complete for <PERSON>z<PERSON><PERSON>!

## ✅ What We've Done

### 1. Fixed CORS Issue
- **Problem**: Admin panel couldn't access backend due to missing CORS headers
- **Solution**: Added `x-user-id` and `x-user-role` to allowed headers in `packages/backend/src/main.ts`
- **Status**: ✅ RESOLVED

### 2. Installed & Configured Ngrok
- **Installed**: Ngrok via Windows Package Manager
- **Created**: Multiple setup scripts and configuration files
- **Status**: ✅ READY TO USE

## 🚀 Quick Start Guide

### Step 1: Setup Ngrok Account
1. Go to [ngrok.com](https://ngrok.com) and create a free account
2. Get your auth token from the dashboard
3. **Restart your terminal** (important!)
4. Run: `ngrok config add-authtoken YOUR_TOKEN_HERE`

### Step 2: Start Your Services
```bash
# Make sure these are running:
# Terminal 1: Backend (Port 7000)
cd packages/backend && yarn start:dev

# Terminal 2: Admin Panel (Port 5000)  
cd packages/admin && yarn dev

# Terminal 3: Website (Port 3000)
cd packages/website && yarn dev
```

### Step 3: Start Ngrok Tunnels
Choose one method:

#### Option A: Use Our Script (Recommended)
```bash
# Double-click or run:
./start-ngrok.bat

# Or PowerShell:
./start-ngrok.ps1
```

#### Option B: Manual Commands
```bash
# Start each in separate terminals:
ngrok http 7000  # Backend API
ngrok http 5000  # Admin Panel
ngrok http 3000  # Website
```

### Step 4: Update CORS (After Getting URLs)
After starting ngrok, you'll get URLs like:
- Backend: `https://abc123.ngrok.io`
- Admin: `https://def456.ngrok.io`
- Website: `https://ghi789.ngrok.io`

**Update `packages/backend/src/main.ts`:**
```typescript
app.enableCors({
  origin: [
    "http://localhost:3000",
    "http://localhost:5000",
    "https://abc123.ngrok.io",    // Your actual backend URL
    "https://def456.ngrok.io",    // Your actual admin URL
    "https://ghi789.ngrok.io",    // Your actual website URL
    "https://azkuja.com",
    "https://admin.azkuja.com"
  ],
  // ... rest of config
});
```

### Step 5: Share & Test
Share these URLs with your testers:
- **Admin Panel**: `https://def456.ngrok.io`
- **Website**: `https://ghi789.ngrok.io`
- **API**: `https://abc123.ngrok.io`

## 📁 Files Created

| File | Purpose |
|------|---------|
| `NGROK_GUIDE.md` | Comprehensive setup guide |
| `ngrok-setup.md` | Technical setup instructions |
| `start-ngrok.bat` | Windows batch script |
| `start-ngrok.ps1` | PowerShell script |
| `ngrok.yml` | Configuration file template |

## 🔧 Current Status

### ✅ Working
- Backend API (Port 7000) - Running with fixed CORS
- Admin Panel (Port 5000) - Can now access backend
- Website (Port 3000) - Ready for testing
- Ngrok installed and configured

### 🔄 Next Steps
1. Create ngrok account and get auth token
2. Start ngrok tunnels
3. Update CORS with your actual ngrok URLs
4. Share URLs with testers

## 🛠️ Troubleshooting

### Common Issues & Solutions

#### 1. "ngrok not found"
**Solution**: Restart your terminal after installation

#### 2. CORS Errors
**Solution**: Make sure to add your ngrok URLs to the CORS configuration

#### 3. Admin Panel Can't Connect
**Solution**: The CORS fix should resolve this, but verify:
- Backend is running on port 7000
- CORS includes your ngrok URLs
- Headers `x-user-id` and `x-user-role` are allowed

#### 4. Services Not Running
**Solution**: Check ports:
```bash
netstat -an | findstr :7000  # Backend
netstat -an | findstr :5000  # Admin
netstat -an | findstr :3000  # Website
```

## 📊 Monitoring

### Ngrok Dashboard
Visit `http://localhost:4040` to see:
- Active tunnels and URLs
- Request/response logs
- Traffic statistics

### Service Health
```bash
# Check if services are running
curl http://localhost:7000/api/restaurants
curl http://localhost:5000
curl http://localhost:3000
```

## 🎯 Testing Scenarios

### Role-Based Testing
1. **Admin**: Use admin panel ngrok URL
2. **Restaurant Owner**: Test both admin and website URLs
3. **Customer**: Use website ngrok URL

### Cross-Device Testing
- Share URLs with mobile devices
- Test on different browsers
- Verify responsive design

## 🔒 Security Notes

⚠️ **Important**: 
- Ngrok is for testing only, not production
- URLs change on free plan restarts
- Anyone with the URL can access your app
- Monitor usage via ngrok dashboard

## 📞 Support

If you encounter issues:
1. Check the troubleshooting section above
2. Verify all services are running locally first
3. Check ngrok dashboard at `http://localhost:4040`
4. Restart ngrok tunnels if needed

---

**You're all set! 🎉 Happy testing with ngrok!** 