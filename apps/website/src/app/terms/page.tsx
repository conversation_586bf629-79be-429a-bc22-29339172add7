'use client'

import Header from '@/components/Header'
import Footer from '@/components/Footer'
import { useTranslations } from '@/hooks/useTranslations'
import { 
  DocumentTextIcon,
  ScaleIcon,
  ShieldCheckIcon
} from '@heroicons/react/24/outline'

export default function TermsPage() {
  const { language } = useTranslations()

  const content = {
    fa: {
      title: 'قوانین و مقررات',
      subtitle: 'قوانین استفاده از سرویس اذکیا',
      lastUpdated: 'آخرین بروزرسانی:',
      acceptance: 'پذیرش قوانین',
      acceptanceText: 'با استفاده از سرویس اذکیا، شما با این قوانین و مقررات موافقت می‌کنید. اگر با این قوانین موافق نیستید، لطفاً از سرویس استفاده نکنید.',
      services: 'خدمات',
      servicesText: 'اذکیا یک پلتفرم آنلاین برای سفارش غذا از رستوران‌های مختلف است. ما واسطه‌ای بین شما و رستوران‌ها هستیم و مسئولیت کیفیت غذا بر عهده رستوران‌ها است.',
      userAccount: 'حساب کاربری',
      userAccountText: 'برای استفاده از برخی خدمات، ایجاد حساب کاربری الزامی است. شما مسئول حفظ امنیت اطلاعات حساب کاربری خود هستید.',
      orders: 'سفارشات',
      ordersText: 'تمام سفارشات پس از تأیید رستوران قطعی می‌شوند. لغو سفارش بعد از تأیید مستلزم هماهنگی با پشتیبانی است.',
      payments: 'پرداخت‌ها',
      paymentsText: 'پرداخت‌ها از طریق روش‌های امن انجام می‌شود. در صورت مشکل در پرداخت، با پشتیبانی تماس بگیرید.',
      liability: 'مسئولیت',
      liabilityText: 'اذکیا مسئولیت کیفیت غذا، زمان تحویل دقیق، یا مشکلات ناشی از رستوران‌ها را نمی‌پذیرد.',
      termination: 'خاتمه',
      terminationText: 'اذکیا حق دارد در صورت نقض قوانین، دسترسی شما را محدود یا قطع کند.',
      changes: 'تغییرات',
      changesText: 'این قوانین ممکن است تغییر کند. تغییرات از طریق وب‌سایت اطلاع‌رسانی می‌شود.',
      contact: 'تماس',
      contactText: 'برای سوالات درباره این قوانین، با ما تماس بگیرید:'
    },
    en: {
      title: 'Terms of Service',
      subtitle: 'Terms of use for Azkuja service',
      lastUpdated: 'Last updated:',
      acceptance: 'Acceptance of Terms',
      acceptanceText: 'By using Azkuja service, you agree to these terms and conditions. If you do not agree with these terms, please do not use the service.',
      services: 'Services',
      servicesText: 'Azkuja is an online platform for ordering food from various restaurants. We act as an intermediary between you and restaurants, and the responsibility for food quality lies with the restaurants.',
      userAccount: 'User Account',
      userAccountText: 'Creating a user account is required to use some services. You are responsible for maintaining the security of your account information.',
      orders: 'Orders',
      ordersText: 'All orders become final after restaurant confirmation. Canceling an order after confirmation requires coordination with support.',
      payments: 'Payments',
      paymentsText: 'Payments are made through secure methods. In case of payment issues, contact support.',
      liability: 'Liability',
      liabilityText: 'Azkuja does not accept responsibility for food quality, exact delivery time, or issues arising from restaurants.',
      termination: 'Termination',
      terminationText: 'Azkuja reserves the right to limit or terminate your access in case of terms violation.',
      changes: 'Changes',
      changesText: 'These terms may change. Changes will be announced through the website.',
      contact: 'Contact',
      contactText: 'For questions about these terms, contact us:'
    },
    ps: {
      title: 'د خدماتو قوانین',
      subtitle: 'د اذکیا سرویس د کارولو قوانین',
      lastUpdated: 'وروستي تازه کول:',
      acceptance: 'د قوانینو منل',
      acceptanceText: 'د اذکیا سرویس کارولو سره، تاسو د دغو قوانینو او شرایطو سره موافق یاست. که چیرې د دغو قوانینو سره موافق نه یاست، مهرباني وکړئ سرویس مه کاروئ.',
      services: 'خدمات',
      servicesText: 'اذکیا د مختلفو رستورانونو څخه د خوړو امر ورکولو لپاره یو آنلاین پلیټفارم دی. موږ د تاسو او رستورانونو ترمنځ د منځګړي رول لوبوو، او د خوړو د کیفیت مسؤلیت د رستورانونو دی.',
      userAccount: 'د کارونکي حساب',
      userAccountText: 'د ځینو خدماتو کارولو لپاره د کارونکي حساب جوړول اړین دي. تاسو د خپل حساب د معلوماتو د امنیت ساتلو مسؤل یاست.',
      orders: 'امرونه',
      ordersText: 'ټول امرونه د رستوران د تایید وروسته وروستي کیږي. د تایید وروسته د امر لغوه کول د ملاتړ سره همغږي ته اړتیا لري.',
      payments: 'تادیات',
      paymentsText: 'تادیات د خوندي میتودونو له لارې ترسره کیږي. د تادیاتو ستونزو په صورت کې، د ملاتړ سره اړیکه ونیسئ.',
      liability: 'مسؤلیت',
      liabilityText: 'اذکیا د خوړو د کیفیت، د دقیق رسولو وخت، یا د رستورانونو څخه راپیدا شوو ستونزو مسؤلیت نه مني.',
      termination: 'پای ته رسول',
      terminationText: 'اذکیا د دې حق لري چې د قوانینو د سرغړونې په صورت کې ستاسو لاسرسی محدود یا قطع کړي.',
      changes: 'بدلونونه',
      changesText: 'دا قوانین ممکن بدلون ومومي. بدلونونه د ویب پاڼې له لارې اعلان کیږي.',
      contact: 'اړیکه',
      contactText: 'د دغو قوانینو په اړه د پوښتنو لپاره، موږ سره اړیکه ونیسئ:'
    }
  }

  const currentContent = content[language] || content.fa

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header />
      
      <main>
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-primary-50 to-orange-50 dark:from-gray-800 dark:to-gray-900 py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <DocumentTextIcon className="w-16 h-16 text-primary-600 mx-auto mb-6" />
              <h1 className="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6">
                {currentContent.title}
              </h1>
              <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-4">
                {currentContent.subtitle}
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {currentContent.lastUpdated} {language === 'en' ? 'December 2024' : language === 'ps' ? 'د دسمبر ۲۰۲۴' : 'دسامبر ۲۰۲۴'}
              </p>
            </div>
          </div>
        </section>

        {/* Terms Content */}
        <section className="py-16 bg-white dark:bg-gray-800">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="prose prose-lg dark:prose-invert max-w-none">
              
              {/* Acceptance */}
              <div className="mb-12">
                <div className="flex items-center mb-6">
                  <ScaleIcon className="w-8 h-8 text-primary-600 ml-3 rtl:mr-3 rtl:ml-0" />
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                    {currentContent.acceptance}
                  </h2>
                </div>
                <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                  {currentContent.acceptanceText}
                </p>
              </div>

              {/* Services */}
              <div className="mb-12">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                  ۱. {currentContent.services}
                </h3>
                <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                  {currentContent.servicesText}
                </p>
              </div>

              {/* User Account */}
              <div className="mb-12">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                  ۲. {currentContent.userAccount}
                </h3>
                <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                  {currentContent.userAccountText}
                </p>
              </div>

              {/* Orders */}
              <div className="mb-12">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                  ۳. {currentContent.orders}
                </h3>
                <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                  {currentContent.ordersText}
                </p>
              </div>

              {/* Payments */}
              <div className="mb-12">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                  ۴. {currentContent.payments}
                </h3>
                <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                  {currentContent.paymentsText}
                </p>
              </div>

              {/* Liability */}
              <div className="mb-12">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                  ۵. {currentContent.liability}
                </h3>
                <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                  {currentContent.liabilityText}
                </p>
              </div>

              {/* Termination */}
              <div className="mb-12">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                  ۶. {currentContent.termination}
                </h3>
                <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                  {currentContent.terminationText}
                </p>
              </div>

              {/* Changes */}
              <div className="mb-12">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                  ۷. {currentContent.changes}
                </h3>
                <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                  {currentContent.changesText}
                </p>
              </div>

              {/* Contact */}
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                  {currentContent.contact}
                </h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  {currentContent.contactText}
                </p>
                <div className="space-y-2">
                  <p className="text-gray-600 dark:text-gray-300">
                    📧 <EMAIL>
                  </p>
                  <p className="text-gray-600 dark:text-gray-300">
                    📞 +93 70 123 4567
                  </p>
                  <p className="text-gray-600 dark:text-gray-300">
                    📍 {language === 'en' ? 'Kabul, Afghanistan' : language === 'ps' ? 'کابل، افغانستان' : 'کابل، افغانستان'}
                  </p>
                </div>
              </div>

            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  )
} 