# 🌐 Ngrok Setup Guide for Azkuja

This guide will help you set up ngrok to share your Azkuja application with others for testing purposes.

## 🚀 Quick Start

### 1. Prerequisites
- ✅ Backend running on port 7000
- ✅ Admin panel running on port 5000  
- ✅ Website running on port 3000
- ✅ Ngrok installed (already done!)

### 2. Setup Ngrok Account
1. Go to [ngrok.com](https://ngrok.com) and create a free account
2. Copy your auth token from the dashboard
3. **Restart your terminal/PowerShell** (important!)
4. Run: `ngrok config add-authtoken YOUR_TOKEN_HERE`

### 3. Start Tunnels (Choose One Method)

#### Method A: Use Our Scripts (Recommended)
```bash
# Windows Batch
./start-ngrok.bat

# PowerShell
./start-ngrok.ps1
```

#### Method B: Manual Commands
```bash
# Start each in separate terminals
ngrok http 7000  # Backend API
ngrok http 5000  # Admin Panel
ngrok http 3000  # Website
```

#### Method C: Configuration File
1. Update `ngrok.yml` with your auth token
2. Run: `ngrok start --all`

## 📋 Step-by-Step Setup

### Step 1: Start Your Services
```bash
# Terminal 1: Backend
cd packages/backend
yarn start:dev

# Terminal 2: Admin Panel
cd packages/admin
yarn dev

# Terminal 3: Website
cd packages/website
yarn dev
```

### Step 2: Get Your Ngrok URLs
After starting ngrok, you'll get URLs like:
- Backend: `https://abc123.ngrok.io`
- Admin: `https://def456.ngrok.io`
- Website: `https://ghi789.ngrok.io`

### Step 3: Update CORS Configuration
Edit `packages/backend/src/main.ts`:

```typescript
app.enableCors({
  origin: [
    "http://localhost:3000",
    "http://localhost:5000",
    "https://abc123.ngrok.io",    // Your backend ngrok URL
    "https://def456.ngrok.io",    // Your admin ngrok URL
    "https://ghi789.ngrok.io",    // Your website ngrok URL
    "https://azkuja.com",
    "https://admin.azkuja.com"
  ],
  methods: ["GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"],
  allowedHeaders: ["Content-Type", "Authorization", "Accept", "X-Requested-With", "x-user-id", "x-user-role"],
  credentials: true,
});
```

### Step 4: Update Frontend API URLs (Optional)
If you want the frontend to use ngrok URLs:

**Admin Panel** (`packages/admin/src/providers/dataProvider.ts`):
```typescript
const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:7000/api';
```

**Website** (`packages/website/src/services/api.ts`):
```typescript
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:7000/api';
```

Then create `.env.local` files:
```env
NEXT_PUBLIC_API_URL=https://abc123.ngrok.io/api
```

## 🔧 Configuration Options

### Free Plan Features
- ✅ HTTPS tunnels
- ✅ Web inspection interface
- ✅ Multiple tunnels (one at a time)
- ❌ Custom subdomains
- ❌ Multiple simultaneous tunnels

### Paid Plan Features ($8/month)
- ✅ Custom subdomains
- ✅ Multiple simultaneous tunnels
- ✅ Password protection
- ✅ Higher connection limits

## 📱 Sharing with Testers

### What to Share
1. **Backend API**: `https://abc123.ngrok.io`
2. **Admin Panel**: `https://def456.ngrok.io`
3. **Website**: `https://ghi789.ngrok.io`

### Testing Instructions for Others
```
1. Open the admin panel: https://def456.ngrok.io
2. Open the website: https://ghi789.ngrok.io
3. The backend API is available at: https://abc123.ngrok.io
```

## 🛠️ Troubleshooting

### Common Issues

#### 1. "ngrok not found"
**Solution**: Restart your terminal after installation

#### 2. CORS Errors
**Solution**: Add your ngrok URLs to the CORS configuration in `main.ts`

#### 3. "Tunnel not found"
**Solution**: Make sure your local services are running first

#### 4. "Connection refused"
**Solution**: Check if the correct ports are being used:
```bash
netstat -an | findstr :7000
netstat -an | findstr :5000
netstat -an | findstr :3000
```

### Useful Commands
```bash
# Check ngrok status
curl http://localhost:4040/api/tunnels

# Kill all ngrok processes
taskkill /f /im ngrok.exe

# Check running services
netstat -an | findstr :7000
netstat -an | findstr :5000
netstat -an | findstr :3000
```

## 🔒 Security Considerations

### ⚠️ Important Security Notes
- **Never use ngrok in production**
- **URLs change on free plan restarts**
- **Anyone with the URL can access your app**
- **Monitor the ngrok dashboard for activity**

### Best Practices
1. Use password protection for sensitive testing
2. Share URLs only with trusted testers
3. Stop tunnels when not needed
4. Monitor traffic via http://localhost:4040
5. Use different URLs for different test environments

## 📊 Monitoring

### Ngrok Dashboard
Visit `http://localhost:4040` to see:
- Active tunnels
- Request/response logs
- Traffic statistics
- Connection details

### Logs
Check the ngrok terminal windows for:
- Connection attempts
- Error messages
- Traffic patterns

## 🎯 Testing Scenarios

### Role-Based Testing
1. **Admin User**: Test via admin panel ngrok URL
2. **Restaurant Owner**: Test both website and admin URLs
3. **Customer**: Test website ngrok URL only

### Cross-Device Testing
- Share URLs with mobile devices
- Test on different browsers
- Verify responsive design

### API Testing
Use tools like Postman with your ngrok backend URL:
```
GET https://abc123.ngrok.io/api/restaurants
POST https://abc123.ngrok.io/api/auth/login
```

## 📝 Environment Variables

Create `.env.ngrok` for easy switching:
```env
NEXT_PUBLIC_API_URL=https://abc123.ngrok.io/api
NEXT_PUBLIC_WEBSITE_URL=https://ghi789.ngrok.io
NEXT_PUBLIC_ADMIN_URL=https://def456.ngrok.io
```

## 🔄 Workflow

### Daily Development
1. Start local services
2. Run ngrok script
3. Share URLs with team
4. Update CORS if URLs changed
5. Stop tunnels when done

### Demo/Testing Sessions
1. Ensure all services are running
2. Start ngrok tunnels
3. Share specific URLs based on user roles
4. Monitor traffic during testing
5. Collect feedback
6. Stop tunnels after session

## 📞 Support

If you encounter issues:
1. Check the troubleshooting section
2. Verify all services are running
3. Check ngrok dashboard at localhost:4040
4. Restart ngrok tunnels if needed

---

**Happy Testing! 🎉** 