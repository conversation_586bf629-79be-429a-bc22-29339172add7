'use client';

import React, { useState } from 'react';
import { useDataGrid } from '@refinedev/mui';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import {
  Box,
  Button,
  Card,
  CardContent,
  Stack,
  Typography,
  Chip,
  Avatar,
  IconButton,
  Tooltip,
  TextField,
} from '@mui/material';
import { useGo } from '@refinedev/core';
import {
  Visibility as ViewIcon,
  Delete as DeleteIcon,
  Favorite as FavoriteIcon,
  Restaurant as RestaurantIcon,
  Person as PersonIcon,
  Search as SearchIcon,
} from '@mui/icons-material';

export const dynamic = 'force-dynamic';

export default function FavoritesList() {
  const [search, setSearch] = useState('');
  const { dataGridProps, filters, setFilters } = useDataGrid({
    resource: 'favorites',
    pagination: { mode: 'server' },
    filters: { mode: 'server' },
    sorters: {
      mode: 'server',
      initial: [{ field: 'created_at', order: 'desc' }],
    },
  });
  const go = useGo();

  const columns: GridColDef[] = [
    {
      field: 'id',
      headerName: 'ID',
      width: 80,
    },
    {
      field: 'user',
      headerName: 'User',
      width: 200,
      renderCell: params => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Avatar sx={{ width: 32, height: 32, bgcolor: 'primary.main' }}>
            <PersonIcon fontSize="small" />
          </Avatar>
          <Box>
            <Typography variant="body2" fontWeight="medium">
              {params.row.user?.name || 'Unknown User'}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {params.row.user?.email}
            </Typography>
          </Box>
        </Box>
      ),
    },
    {
      field: 'restaurant',
      headerName: 'Restaurant',
      width: 250,
      renderCell: params => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Avatar sx={{ width: 32, height: 32, bgcolor: 'secondary.main' }}>
            <RestaurantIcon fontSize="small" />
          </Avatar>
          <Box>
            <Typography variant="body2" fontWeight="medium">
              {params.row.restaurant?.name || 'Unknown Restaurant'}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {params.row.restaurant?.city}
            </Typography>
          </Box>
        </Box>
      ),
    },
    {
      field: 'created_at',
      headerName: 'Added Date',
      width: 150,
      renderCell: params => (
        <Typography variant="body2">
          {new Date(params.value).toLocaleDateString()}
        </Typography>
      ),
    },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 120,
      sortable: false,
      filterable: false,
      renderCell: params => (
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Tooltip title="View Details">
            <IconButton
              size="small"
              onClick={() => go({ to: `/favorites/show/${params.row.id}` })}
            >
              <ViewIcon fontSize="small" />
            </IconButton>
          </Tooltip>
          <Tooltip title="Remove Favorite">
            <IconButton
              size="small"
              color="error"
              onClick={() => {
                // Handle delete action
                console.log('Delete favorite:', params.row.id);
              }}
            >
              <DeleteIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </Box>
      ),
    },
  ];

  const handleSearch = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setFilters([
      {
        field: 'search',
        operator: 'contains',
        value: search,
      },
    ]);
  };

  return (
    <Stack spacing={3}>
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        <Typography variant="h4" component="h1" fontWeight="bold">
          Favorites Management
        </Typography>
      </Box>

      <Card>
        <CardContent>
          <Box sx={{ mb: 2 }}>
            <form
              onSubmit={handleSearch}
              style={{ display: 'flex', gap: 8, alignItems: 'center' }}
            >
              <TextField
                size="small"
                placeholder="Search by user name, restaurant name..."
                value={search || ''}
                onChange={e => setSearch(e.target.value)}
                sx={{ minWidth: 300 }}
                InputProps={{
                  startAdornment: (
                    <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
                  ),
                }}
              />
              <Button type="submit" variant="contained">
                Search
              </Button>
            </form>
          </Box>

          <Box sx={{ height: 600, width: '100%' }}>
            <DataGrid
              {...dataGridProps}
              columns={columns}
              pageSizeOptions={[10, 25, 50]}
              disableRowSelectionOnClick
              sx={{
                '& .MuiDataGrid-cell': {
                  borderBottom: '1px solid',
                  borderColor: 'divider',
                },
                '& .MuiDataGrid-columnHeaders': {
                  backgroundColor: 'background.default',
                  borderBottom: '2px solid',
                  borderColor: 'divider',
                },
              }}
            />
          </Box>
        </CardContent>
      </Card>
    </Stack>
  );
}
