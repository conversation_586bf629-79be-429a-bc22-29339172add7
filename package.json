{"name": "azkuja-restaurant-directory", "private": true, "version": "1.0.0", "workspaces": ["packages/*"], "scripts": {"build": "turbo build", "dev": "turbo dev", "lint": "turbo lint", "type-check": "turbo type-check", "test": "turbo test", "clean": "turbo clean", "dev:website": "turbo dev --filter=@azkuja/website", "dev:backend": "turbo start:dev --filter=@azkuja/backend", "dev:admin": "turbo dev --filter=@azkuja/admin", "dev:mobile": "turbo start --filter=@azkuja/mobile", "dev:all": "turbo dev --parallel", "build:website": "turbo build --filter=@azkuja/website", "build:backend": "turbo build --filter=@azkuja/backend", "build:admin": "turbo build --filter=@azkuja/admin", "build:shared": "turbo build --filter=@azkuja/shared", "lint:fix": "turbo lint -- --fix", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,json,md}\"", "install:all": "pnpm install", "clean:all": "turbo clean && rm -rf node_modules"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint-config-prettier": "^9.0.0", "husky": "^8.0.3", "lint-staged": "^14.0.1", "prettier": "^3.0.0", "turbo": "^2.5.5", "typescript": "^5.2.2"}, "dependencies": {"@refinedev/inferencer": "^5.1.1", "@refinedev/mui": "^6.2.1", "@types/lodash": "4.14.202", "@types/lodash-es": "4.17.12", "lodash": "4.17.21"}, "packageManager": "pnpm@10.13.1"}