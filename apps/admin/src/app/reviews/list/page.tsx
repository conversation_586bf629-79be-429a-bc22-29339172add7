'use client';

import React, { useState } from 'react';
import { useDataGrid, useAutocomplete } from '@refinedev/mui';
import { DataGrid, GridColDef, GridRowSelectionModel } from '@mui/x-data-grid';
import {
  Box,
  Button,
  Card,
  CardContent,
  Stack,
  Typography,
  Chip,
  Avatar,
  Rating,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tooltip,
  Alert,
  Badge,
} from '@mui/material';
import {
  useGo,
  useMany,
  useInvalidate,
  useNotification,
  useUpdate,
  useUpdateMany,
} from '@refinedev/core';
// Temporarily commenting out shared imports to fix build
// import {
//   REVIEW_STATUS,
//   STATUS_COLORS,
//   STATUS_LABELS
// } from '@azkuja/shared';

// Local constants to replace the shared ones
const REVIEW_STATUS = {
  PENDING: 'pending',
  APPROVED: 'approved',
  REJECTED: 'rejected',
  FLAGGED: 'flagged',
} as const;

const STATUS_COLORS = {
  review_pending: '#ff9800',
  review_approved: '#4caf50',
  review_rejected: '#f44336',
  review_flagged: '#ff5722',
} as const;

const STATUS_LABELS = {
  pending: 'در انتظار بررسی',
  approved: 'تایید شده',
  rejected: 'رد شده',
  flagged: 'گزارش شده',
  review_pending: 'در انتظار بررسی',
  review_approved: 'تایید شده',
  review_rejected: 'رد شده',
  review_flagged: 'گزارش شده',
} as const;
import {
  Visibility as ViewIcon,
  Delete as DeleteIcon,
  Block as BlockIcon,
  CheckCircle as ApproveIcon,
  Flag as FlagIcon,
  Restaurant as RestaurantIcon,
  Person as PersonIcon,
  ThumbUp as ThumbUpIcon,
  ThumbDown as ThumbDownIcon,
  FilterList as FilterIcon,
} from '@mui/icons-material';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

interface Review {
  id: string;
  rating: number;
  comment: string;
  status: 'pending' | 'approved' | 'rejected' | 'flagged';
  isReported: boolean;
  reportCount: number;
  helpfulCount: number;
  created_at: string;
  updated_at: string;
  user: {
    id: string;
    name: string;
    email: string;
    avatar?: string;
  };
  restaurant: {
    id: string;
    name: string;
    city: string;
  };
}

export default function ReviewsList() {
  const go = useGo();
  const invalidate = useInvalidate();
  const { open } = useNotification();

  const [selectedRows, setSelectedRows] = useState<GridRowSelectionModel>([]);
  const [moderationDialog, setModerationDialog] = useState<{
    open: boolean;
    reviewId?: string;
    action?: 'approve' | 'reject' | 'flag';
    reason?: string;
  }>({ open: false });
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [filterRating, setFilterRating] = useState<number | null>(null);
  const [bulkActionDialog, setBulkActionDialog] = useState<{
    open: boolean;
    action?: 'approve' | 'reject' | 'delete';
  }>({ open: false });

  const { mutate: updateReview } = useUpdate();
  const { mutate: updateManyReviews } = useUpdateMany();

  // Simple review moderation functions
  const moderateReview = async (reviewId: string, action: string) => {
    try {
      // Implementation would go here - for now just log
      console.log(`Moderating review ${reviewId} with action ${action}`);
    } catch (error) {
      console.error('Error moderating review:', error);
    }
  };

  const bulkModerateReviews = async (reviewIds: string[], action: string) => {
    try {
      // Implementation would go here - for now just log
      console.log(
        `Bulk moderating reviews ${reviewIds.join(', ')} with action ${action}`
      );
    } catch (error) {
      console.error('Error bulk moderating reviews:', error);
    }
  };

  // Build filters based on state
  const filters = [
    ...(filterStatus !== 'all'
      ? [{ field: 'status', operator: 'eq' as const, value: filterStatus }]
      : []),
    ...(filterRating
      ? [{ field: 'rating', operator: 'eq' as const, value: filterRating }]
      : []),
  ];

  const { dataGridProps } = useDataGrid({
    resource: 'reviews',
    syncWithLocation: true,
    pagination: {
      mode: 'server',
    },
    filters: {
      mode: 'server',
      initial: filters,
    },
    sorters: {
      mode: 'server',
      initial: [{ field: 'created_at', order: 'desc' }],
    },
  });

  // Get restaurant and user data for the reviews
  const restaurantIds =
    dataGridProps?.rows
      ?.map((item: any) => item.restaurant_id)
      .filter(Boolean) ?? [];
  const { data: restaurantData } = useMany({
    resource: 'restaurants',
    ids: restaurantIds,
  });

  const userIds =
    dataGridProps?.rows?.map((item: any) => item.user_id).filter(Boolean) ?? [];
  const { data: userData } = useMany({
    resource: 'users',
    ids: userIds,
  });

  // Create lookup maps
  const restaurantMap: Record<string, any> = {};
  const userMap: Record<string, any> = {};

  if (restaurantData?.data) {
    restaurantData.data.forEach((item: any) => {
      restaurantMap[item.id.toString()] = item;
    });
  }

  if (userData?.data) {
    userData.data.forEach((item: any) => {
      userMap[item.id.toString()] = item;
    });
  }

  const handleModerationAction = async (
    reviewId: string,
    action: 'approve' | 'reject' | 'flag',
    reason?: string
  ) => {
    const statusMap = {
      approve: REVIEW_STATUS.APPROVED,
      reject: REVIEW_STATUS.REJECTED,
      flag: REVIEW_STATUS.FLAGGED,
    };

    try {
      await moderateReview(reviewId, statusMap[action]);

      open?.({
        type: 'success',
        message: `Review ${action}d successfully`,
      });

      invalidate({
        resource: 'reviews',
        invalidates: ['list'],
      });
    } catch (error) {
      console.error('Error handling moderation action:', error);
      open?.({
        type: 'error',
        message: 'Failed to moderate review.',
      });
    }
  };

  const handleBulkAction = async (action: 'approve' | 'reject' | 'delete') => {
    try {
      if (action === 'delete') {
        // Handle bulk delete
        await Promise.all(
          selectedRows.map(id =>
            updateReview({
              resource: 'reviews',
              id: id.toString(),
              values: { deleted_at: new Date().toISOString() },
            })
          )
        );
      } else {
        // Handle bulk status update
        const statusMap = {
          approve: REVIEW_STATUS.APPROVED,
          reject: REVIEW_STATUS.REJECTED,
        };

        await bulkModerateReviews(
          selectedRows.map(id => id.toString()),
          statusMap[action]
        );
      }

      open?.({
        type: 'success',
        message: `${selectedRows.length} reviews ${action}d successfully`,
      });

      setSelectedRows([]);
      setBulkActionDialog({ open: false });

      invalidate({
        resource: 'reviews',
        invalidates: ['list'],
      });
    } catch (error) {
      console.error('Error handling bulk action:', error);
      open?.({
        type: 'error',
        message: 'Failed to perform bulk action.',
      });
    }
  };

  const getStatusColor = (status: string) => {
    return STATUS_COLORS[status as keyof typeof STATUS_COLORS] || 'default';
  };

  const getStatusLabel = (status: string) => {
    return STATUS_LABELS[status as keyof typeof STATUS_LABELS] || status;
  };

  const columns: GridColDef[] = [
    {
      field: 'id',
      headerName: 'شناسه',
      width: 80,
      renderCell: ({ value }) => (
        <Typography variant="body2" color="text.secondary">
          #{value?.toString().slice(-6)}
        </Typography>
      ),
    },
    {
      field: 'user_id',
      headerName: 'کاربر',
      width: 180,
      renderCell: ({ row }) => {
        const user = userMap[row.user_id?.toString()];
        return (
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Avatar sx={{ width: 32, height: 32 }}>
              {user?.avatar ? (
                <img src={user.avatar} alt={user.name} />
              ) : (
                <PersonIcon />
              )}
            </Avatar>
            <Box>
              <Typography variant="body2" fontWeight="medium">
                {user?.name || 'نامشخص'}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {user?.email || ''}
              </Typography>
            </Box>
          </Box>
        );
      },
    },
    {
      field: 'restaurant_id',
      headerName: 'رستوران',
      width: 180,
      renderCell: ({ row }) => {
        const restaurant = restaurantMap[row.restaurant_id?.toString()];
        return (
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <RestaurantIcon color="primary" />
            <Box>
              <Typography variant="body2" fontWeight="medium">
                {restaurant?.name || 'نامشخص'}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {restaurant?.city || ''}
              </Typography>
            </Box>
          </Box>
        );
      },
    },
    {
      field: 'rating',
      headerName: 'امتیاز',
      width: 120,
      renderCell: ({ value }) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Rating value={value} readOnly size="small" />
          <Typography variant="body2" color="text.secondary">
            ({value})
          </Typography>
        </Box>
      ),
    },
    {
      field: 'comment',
      headerName: 'نظر',
      width: 300,
      renderCell: ({ value }) => (
        <Tooltip title={value || ''} arrow>
          <Typography
            variant="body2"
            sx={{
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              maxWidth: '280px',
            }}
          >
            {value || 'بدون نظر'}
          </Typography>
        </Tooltip>
      ),
    },
    {
      field: 'status',
      headerName: 'وضعیت',
      width: 120,
      renderCell: ({ value, row }) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Chip
            label={getStatusLabel(value)}
            color={getStatusColor(value) as any}
            size="small"
          />
          {row.is_reported && (
            <Badge badgeContent={row.report_count || 1} color="error">
              <FlagIcon fontSize="small" color="error" />
            </Badge>
          )}
        </Box>
      ),
    },
    {
      field: 'helpful_count',
      headerName: 'مفید',
      width: 80,
      renderCell: ({ value }) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
          <ThumbUpIcon fontSize="small" color="primary" />
          <Typography variant="body2">{value || 0}</Typography>
        </Box>
      ),
    },
    {
      field: 'created_at',
      headerName: 'تاریخ',
      width: 120,
      renderCell: ({ value }) => (
        <Typography variant="body2" color="text.secondary">
          {new Date(value).toLocaleDateString('fa-IR')}
        </Typography>
      ),
    },
    {
      field: 'actions',
      headerName: 'عملیات',
      width: 200,
      sortable: false,
      renderCell: ({ row }) => (
        <Box sx={{ display: 'flex', gap: 0.5 }}>
          <Tooltip title="مشاهده جزئیات">
            <IconButton
              size="small"
              onClick={() => go({ to: `/reviews/show/${row.id}` })}
            >
              <ViewIcon />
            </IconButton>
          </Tooltip>

          {row.status === 'pending' && (
            <>
              <Tooltip title="تایید">
                <IconButton
                  size="small"
                  color="success"
                  onClick={() => handleModerationAction(row.id, 'approve')}
                >
                  <ApproveIcon />
                </IconButton>
              </Tooltip>
              <Tooltip title="رد">
                <IconButton
                  size="small"
                  color="error"
                  onClick={() =>
                    setModerationDialog({
                      open: true,
                      reviewId: row.id,
                      action: 'reject',
                    })
                  }
                >
                  <BlockIcon />
                </IconButton>
              </Tooltip>
            </>
          )}

          <Tooltip title="علامت‌گذاری">
            <IconButton
              size="small"
              color="warning"
              onClick={() =>
                setModerationDialog({
                  open: true,
                  reviewId: row.id,
                  action: 'flag',
                })
              }
            >
              <FlagIcon />
            </IconButton>
          </Tooltip>

          <Tooltip title="حذف">
            <IconButton
              size="small"
              color="error"
              onClick={() =>
                setModerationDialog({
                  open: true,
                  reviewId: row.id,
                  action: 'reject',
                })
              }
            >
              <DeleteIcon />
            </IconButton>
          </Tooltip>
        </Box>
      ),
    },
  ];

  return (
    <Box>
      <Card sx={{ mb: 2 }}>
        <CardContent>
          <Stack
            direction="row"
            justifyContent="space-between"
            alignItems="center"
            mb={2}
          >
            <Typography variant="h5" component="h1">
              مدیریت نظرات
            </Typography>
            <Box sx={{ display: 'flex', gap: 1 }}>
              {selectedRows.length > 0 && (
                <>
                  <Button
                    variant="contained"
                    color="success"
                    onClick={() =>
                      setBulkActionDialog({ open: true, action: 'approve' })
                    }
                  >
                    تایید گروهی ({selectedRows.length})
                  </Button>
                  <Button
                    variant="contained"
                    color="error"
                    onClick={() =>
                      setBulkActionDialog({ open: true, action: 'reject' })
                    }
                  >
                    رد گروهی ({selectedRows.length})
                  </Button>
                </>
              )}
            </Box>
          </Stack>

          {/* Filters */}
          <Stack direction="row" spacing={2} mb={2}>
            <FormControl size="small" sx={{ minWidth: 150 }}>
              <InputLabel>وضعیت</InputLabel>
              <Select
                value={filterStatus}
                label="وضعیت"
                onChange={e => setFilterStatus(e.target.value)}
              >
                <MenuItem value="all">همه</MenuItem>
                <MenuItem value={REVIEW_STATUS.PENDING}>
                  {STATUS_LABELS[REVIEW_STATUS.PENDING]}
                </MenuItem>
                <MenuItem value={REVIEW_STATUS.APPROVED}>
                  {STATUS_LABELS[REVIEW_STATUS.APPROVED]}
                </MenuItem>
                <MenuItem value={REVIEW_STATUS.REJECTED}>
                  {STATUS_LABELS[REVIEW_STATUS.REJECTED]}
                </MenuItem>
                <MenuItem value={REVIEW_STATUS.FLAGGED}>
                  {STATUS_LABELS[REVIEW_STATUS.FLAGGED]}
                </MenuItem>
              </Select>
            </FormControl>

            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel>امتیاز</InputLabel>
              <Select
                value={filterRating || ''}
                label="امتیاز"
                onChange={e =>
                  setFilterRating(
                    e.target.value ? Number(e.target.value) : null
                  )
                }
              >
                <MenuItem value="">همه</MenuItem>
                <MenuItem value={5}>5 ستاره</MenuItem>
                <MenuItem value={4}>4 ستاره</MenuItem>
                <MenuItem value={3}>3 ستاره</MenuItem>
                <MenuItem value={2}>2 ستاره</MenuItem>
                <MenuItem value={1}>1 ستاره</MenuItem>
              </Select>
            </FormControl>
          </Stack>

          {/* Statistics */}
          <Alert severity="info" sx={{ mb: 2 }}>
            <Typography variant="body2">
              مجموع نظرات: {dataGridProps.rowCount || 0} | در انتظار تایید:{' '}
              {dataGridProps.rows?.filter((r: any) => r.status === 'pending')
                .length || 0}{' '}
              | علامت‌گذاری شده:{' '}
              {dataGridProps.rows?.filter((r: any) => r.is_reported).length ||
                0}
            </Typography>
          </Alert>
        </CardContent>
      </Card>

      <Card>
        <DataGrid
          {...dataGridProps}
          columns={columns}
          checkboxSelection
          disableRowSelectionOnClick
          onRowSelectionModelChange={setSelectedRows}
          rowSelectionModel={selectedRows}
          autoHeight
          sx={{
            '& .MuiDataGrid-row': {
              cursor: 'pointer',
            },
          }}
        />
      </Card>

      {/* Moderation Dialog */}
      <Dialog
        open={moderationDialog.open}
        onClose={() => setModerationDialog({ open: false })}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          {moderationDialog.action === 'approve' && 'تایید نظر'}
          {moderationDialog.action === 'reject' && 'رد نظر'}
          {moderationDialog.action === 'flag' && 'علامت‌گذاری نظر'}
        </DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            multiline
            rows={3}
            label="دلیل (اختیاری)"
            value={moderationDialog.reason || ''}
            onChange={e =>
              setModerationDialog(prev => ({ ...prev, reason: e.target.value }))
            }
            sx={{ mt: 2 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setModerationDialog({ open: false })}>
            انصراف
          </Button>
          <Button
            variant="contained"
            onClick={() => {
              if (moderationDialog.reviewId && moderationDialog.action) {
                handleModerationAction(
                  moderationDialog.reviewId,
                  moderationDialog.action,
                  moderationDialog.reason
                );
              }
              setModerationDialog({ open: false });
            }}
          >
            تایید
          </Button>
        </DialogActions>
      </Dialog>

      {/* Bulk Action Dialog */}
      <Dialog
        open={bulkActionDialog.open}
        onClose={() => setBulkActionDialog({ open: false })}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          {bulkActionDialog.action === 'approve' &&
            `تایید ${selectedRows.length} نظر`}
          {bulkActionDialog.action === 'reject' &&
            `رد ${selectedRows.length} نظر`}
          {bulkActionDialog.action === 'delete' &&
            `حذف ${selectedRows.length} نظر`}
        </DialogTitle>
        <DialogContent>
          <Typography>آیا از انجام این عملیات اطمینان دارید؟</Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setBulkActionDialog({ open: false })}>
            انصراف
          </Button>
          <Button
            variant="contained"
            color={bulkActionDialog.action === 'approve' ? 'success' : 'error'}
            onClick={() => {
              if (bulkActionDialog.action) {
                handleBulkAction(bulkActionDialog.action);
              }
            }}
          >
            تایید
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}
