'use client';

import React, { useState } from 'react';
import { useDataGrid } from '@refinedev/mui';
import { DataGrid, GridColDef, GridRowSelectionModel } from '@mui/x-data-grid';
import {
  Box,
  Button,
  Card,
  CardContent,
  Stack,
  Typography,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Avatar,
  IconButton,
  Tooltip,
  Alert,
  Autocomplete,
  Switch,
  FormControlLabel,
  Divider,
  Grid,
} from '@mui/material';
import {
  useGo,
  useMany,
  useInvalidate,
  useNotification,
  useList,
  useUpdate,
  useUpdateMany,
  useDelete,
  useCreate,
} from '@refinedev/core';
import {
  Add as AddIcon,
  Send as SendIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Visibility as ViewIcon,
  NotificationsActive as NotificationIcon,
  Group as GroupIcon,
  Person as PersonIcon,
  Campaign as CampaignIcon,
  Schedule as ScheduleIcon,
} from '@mui/icons-material';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

interface NotificationFormData {
  title: string;
  message: string;
  type:
    | 'info'
    | 'success'
    | 'warning'
    | 'error'
    | 'promotion'
    | 'order_update'
    | 'system';
  target: 'all' | 'specific' | 'role' | 'restaurant_customers';
  targetUsers?: string[];
  targetRole?: 'customer' | 'restaurant_owner' | 'admin';
  targetRestaurant?: string;
  scheduledAt?: Date;
  isScheduled: boolean;
  priority: 'low' | 'normal' | 'high';
  includeEmail: boolean;
  includeSms: boolean;
  includePush: boolean;
  actionUrl?: string;
  imageUrl?: string;
}

export default function NotificationsList() {
  const go = useGo();
  const invalidate = useInvalidate();
  const { open } = useNotification();

  const [selectedRows, setSelectedRows] = useState<GridRowSelectionModel>([]);
  const [createDialog, setCreateDialog] = useState(false);
  const [bulkDeleteDialog, setBulkDeleteDialog] = useState(false);
  const [formData, setFormData] = useState<NotificationFormData>({
    title: '',
    message: '',
    type: 'info',
    target: 'all',
    isScheduled: false,
    priority: 'normal',
    includeEmail: true,
    includeSms: false,
    includePush: true,
  });

  const { mutate: createNotification } = useCreate();
  const { mutate: deleteNotification } = useDelete();

  const { dataGridProps } = useDataGrid({
    resource: 'notifications',
    syncWithLocation: true,
    pagination: {
      mode: 'server',
    },
    sorters: {
      mode: 'server',
      initial: [{ field: 'created_at', order: 'desc' }],
    },
  });

  // Get users for autocomplete (only when needed)
  const { data: usersData } = useMany({
    resource: 'users',
    ids: formData.target === 'specific' ? [] : [],
  });

  // Get restaurants for autocomplete (only when needed)
  const { data: restaurantsData } = useMany({
    resource: 'restaurants',
    ids: formData.target === 'restaurant_customers' ? [] : [],
  });

  const handleCreateNotification = async () => {
    try {
      const notificationData = {
        title: formData.title,
        message: formData.message,
        type: formData.type,
        priority: formData.priority,
        ...(formData.isScheduled &&
          formData.scheduledAt && {
            scheduled_at: formData.scheduledAt.toISOString(),
          }),
        ...(formData.actionUrl && { action_url: formData.actionUrl }),
        ...(formData.imageUrl && { image_url: formData.imageUrl }),
        delivery_methods: {
          push: formData.includePush,
          email: formData.includeEmail,
          sms: formData.includeSms,
        },
        // Target configuration
        ...(formData.target === 'specific' && {
          user_ids: formData.targetUsers,
        }),
        ...(formData.target === 'role' && {
          target_role: formData.targetRole,
        }),
        ...(formData.target === 'restaurant_customers' && {
          restaurant_id: formData.targetRestaurant,
        }),
      };

      await createNotification({
        resource: 'notifications',
        values: notificationData,
      });

      open?.({
        type: 'success',
        message: 'اعلان با موفقیت ارسال شد',
      });

      setCreateDialog(false);
      setFormData({
        title: '',
        message: '',
        type: 'info',
        target: 'all',
        isScheduled: false,
        priority: 'normal',
        includeEmail: true,
        includeSms: false,
        includePush: true,
      });

      invalidate({
        resource: 'notifications',
        invalidates: ['list'],
      });
    } catch (error) {
      open?.({
        type: 'error',
        message: 'خطا در ارسال اعلان',
      });
    }
  };

  const handleBulkDelete = async () => {
    try {
      await Promise.all(
        selectedRows.map(id =>
          deleteNotification({
            resource: 'notifications',
            id: id.toString(),
          })
        )
      );

      open?.({
        type: 'success',
        message: `${selectedRows.length} اعلان حذف شد`,
      });

      setSelectedRows([]);
      setBulkDeleteDialog(false);

      invalidate({
        resource: 'notifications',
        invalidates: ['list'],
      });
    } catch (error) {
      open?.({
        type: 'error',
        message: 'خطا در حذف اعلان‌ها',
      });
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'success':
        return 'success';
      case 'warning':
        return 'warning';
      case 'error':
        return 'error';
      case 'promotion':
        return 'secondary';
      case 'order_update':
        return 'primary';
      case 'system':
        return 'default';
      default:
        return 'info';
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'success':
        return 'موفقیت';
      case 'warning':
        return 'هشدار';
      case 'error':
        return 'خطا';
      case 'promotion':
        return 'تبلیغات';
      case 'order_update':
        return 'بروزرسانی سفارش';
      case 'system':
        return 'سیستم';
      default:
        return 'اطلاعات';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'error';
      case 'normal':
        return 'primary';
      case 'low':
        return 'default';
      default:
        return 'default';
    }
  };

  const columns: GridColDef[] = [
    {
      field: 'id',
      headerName: 'شناسه',
      width: 80,
      renderCell: ({ value }) => (
        <Typography variant="body2" color="text.secondary">
          #{value?.toString().slice(-6)}
        </Typography>
      ),
    },
    {
      field: 'title',
      headerName: 'عنوان',
      width: 200,
      renderCell: ({ value }) => (
        <Typography variant="body2" fontWeight="medium">
          {value}
        </Typography>
      ),
    },
    {
      field: 'message',
      headerName: 'پیام',
      width: 300,
      renderCell: ({ value }) => (
        <Tooltip title={value || ''} arrow>
          <Typography
            variant="body2"
            sx={{
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              maxWidth: '280px',
            }}
          >
            {value}
          </Typography>
        </Tooltip>
      ),
    },
    {
      field: 'type',
      headerName: 'نوع',
      width: 120,
      renderCell: ({ value }) => (
        <Chip
          label={getTypeLabel(value)}
          color={getTypeColor(value) as any}
          size="small"
        />
      ),
    },
    {
      field: 'priority',
      headerName: 'اولویت',
      width: 100,
      renderCell: ({ value }) => (
        <Chip
          label={
            value === 'high' ? 'بالا' : value === 'normal' ? 'معمولی' : 'پایین'
          }
          color={getPriorityColor(value) as any}
          size="small"
          variant="outlined"
        />
      ),
    },
    {
      field: 'target_count',
      headerName: 'مخاطبین',
      width: 100,
      renderCell: ({ row }) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
          <GroupIcon fontSize="small" color="primary" />
          <Typography variant="body2">{row.target_count || 0}</Typography>
        </Box>
      ),
    },
    {
      field: 'delivery_status',
      headerName: 'وضعیت ارسال',
      width: 150,
      renderCell: ({ row }) => (
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
          <Typography variant="body2">
            ارسال شده: {row.delivered_count || 0}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            خوانده شده: {row.read_count || 0}
          </Typography>
        </Box>
      ),
    },
    {
      field: 'scheduled_at',
      headerName: 'زمان‌بندی',
      width: 150,
      renderCell: ({ value, row }) => (
        <Box>
          {value ? (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <ScheduleIcon fontSize="small" color="warning" />
              <Typography variant="body2">
                {new Date(value).toLocaleDateString('fa-IR')}
              </Typography>
            </Box>
          ) : (
            <Typography variant="body2" color="text.secondary">
              {new Date(row.created_at).toLocaleDateString('fa-IR')}
            </Typography>
          )}
        </Box>
      ),
    },
    {
      field: 'actions',
      headerName: 'عملیات',
      width: 150,
      sortable: false,
      renderCell: ({ row }) => (
        <Box sx={{ display: 'flex', gap: 0.5 }}>
          <Tooltip title="مشاهده جزئیات">
            <IconButton
              size="small"
              onClick={() => go({ to: `/notifications/show/${row.id}` })}
            >
              <ViewIcon />
            </IconButton>
          </Tooltip>

          <Tooltip title="حذف">
            <IconButton
              size="small"
              color="error"
              onClick={() => {
                deleteNotification({
                  resource: 'notifications',
                  id: row.id,
                });
              }}
            >
              <DeleteIcon />
            </IconButton>
          </Tooltip>
        </Box>
      ),
    },
  ];

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Box>
        <Card sx={{ mb: 2 }}>
          <CardContent>
            <Stack
              direction="row"
              justifyContent="space-between"
              alignItems="center"
              mb={2}
            >
              <Typography variant="h5" component="h1">
                مدیریت اعلان‌ها
              </Typography>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={() => setCreateDialog(true)}
                >
                  ارسال اعلان جدید
                </Button>
                {selectedRows.length > 0 && (
                  <Button
                    variant="contained"
                    color="error"
                    startIcon={<DeleteIcon />}
                    onClick={() => setBulkDeleteDialog(true)}
                  >
                    حذف گروهی ({selectedRows.length})
                  </Button>
                )}
              </Box>
            </Stack>

            {/* Statistics */}
            <Alert severity="info" sx={{ mb: 2 }}>
              <Typography variant="body2">
                مجموع اعلان‌ها: {dataGridProps.rowCount || 0} | امروز:{' '}
                {dataGridProps.rows?.filter(
                  (r: any) =>
                    new Date(r.created_at).toDateString() ===
                    new Date().toDateString()
                ).length || 0}{' '}
                | زمان‌بندی شده:{' '}
                {dataGridProps.rows?.filter((r: any) => r.scheduled_at)
                  .length || 0}
              </Typography>
            </Alert>
          </CardContent>
        </Card>

        <Card>
          <DataGrid
            {...dataGridProps}
            columns={columns}
            checkboxSelection
            disableRowSelectionOnClick
            onRowSelectionModelChange={setSelectedRows}
            rowSelectionModel={selectedRows}
            autoHeight
            sx={{
              '& .MuiDataGrid-row': {
                cursor: 'pointer',
              },
            }}
          />
        </Card>

        {/* Create Notification Dialog */}
        <Dialog
          open={createDialog}
          onClose={() => setCreateDialog(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>ارسال اعلان جدید</DialogTitle>
          <DialogContent>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="عنوان اعلان"
                  value={formData.title}
                  onChange={e =>
                    setFormData(prev => ({ ...prev, title: e.target.value }))
                  }
                  required
                />
              </Grid>

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  multiline
                  rows={4}
                  label="متن پیام"
                  value={formData.message}
                  onChange={e =>
                    setFormData(prev => ({ ...prev, message: e.target.value }))
                  }
                  required
                />
              </Grid>

              <Grid item xs={6}>
                <FormControl fullWidth>
                  <InputLabel>نوع اعلان</InputLabel>
                  <Select
                    value={formData.type}
                    label="نوع اعلان"
                    onChange={e =>
                      setFormData(prev => ({
                        ...prev,
                        type: e.target.value as any,
                      }))
                    }
                  >
                    <MenuItem value="info">اطلاعات</MenuItem>
                    <MenuItem value="success">موفقیت</MenuItem>
                    <MenuItem value="warning">هشدار</MenuItem>
                    <MenuItem value="error">خطا</MenuItem>
                    <MenuItem value="promotion">تبلیغات</MenuItem>
                    <MenuItem value="order_update">بروزرسانی سفارش</MenuItem>
                    <MenuItem value="system">سیستم</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={6}>
                <FormControl fullWidth>
                  <InputLabel>اولویت</InputLabel>
                  <Select
                    value={formData.priority}
                    label="اولویت"
                    onChange={e =>
                      setFormData(prev => ({
                        ...prev,
                        priority: e.target.value as any,
                      }))
                    }
                  >
                    <MenuItem value="low">پایین</MenuItem>
                    <MenuItem value="normal">معمولی</MenuItem>
                    <MenuItem value="high">بالا</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12}>
                <FormControl fullWidth>
                  <InputLabel>مخاطبین</InputLabel>
                  <Select
                    value={formData.target}
                    label="مخاطبین"
                    onChange={e =>
                      setFormData(prev => ({
                        ...prev,
                        target: e.target.value as any,
                      }))
                    }
                  >
                    <MenuItem value="all">همه کاربران</MenuItem>
                    <MenuItem value="specific">کاربران خاص</MenuItem>
                    <MenuItem value="role">بر اساس نقش</MenuItem>
                    <MenuItem value="restaurant_customers">
                      مشتریان رستوران
                    </MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              {formData.target === 'role' && (
                <Grid item xs={12}>
                  <FormControl fullWidth>
                    <InputLabel>نقش</InputLabel>
                    <Select
                      value={formData.targetRole || ''}
                      label="نقش"
                      onChange={e =>
                        setFormData(prev => ({
                          ...prev,
                          targetRole: e.target.value as any,
                        }))
                      }
                    >
                      <MenuItem value="customer">مشتری</MenuItem>
                      <MenuItem value="restaurant_owner">صاحب رستوران</MenuItem>
                      <MenuItem value="admin">مدیر</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
              )}

              <Grid item xs={12}>
                <Divider sx={{ my: 2 }} />
                <Typography variant="subtitle2" sx={{ mb: 2 }}>
                  روش‌های ارسال
                </Typography>
                <Stack direction="row" spacing={2}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.includePush}
                        onChange={e =>
                          setFormData(prev => ({
                            ...prev,
                            includePush: e.target.checked,
                          }))
                        }
                      />
                    }
                    label="اعلان Push"
                  />
                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.includeEmail}
                        onChange={e =>
                          setFormData(prev => ({
                            ...prev,
                            includeEmail: e.target.checked,
                          }))
                        }
                      />
                    }
                    label="ایمیل"
                  />
                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.includeSms}
                        onChange={e =>
                          setFormData(prev => ({
                            ...prev,
                            includeSms: e.target.checked,
                          }))
                        }
                      />
                    }
                    label="پیامک"
                  />
                </Stack>
              </Grid>

              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={formData.isScheduled}
                      onChange={e =>
                        setFormData(prev => ({
                          ...prev,
                          isScheduled: e.target.checked,
                        }))
                      }
                    />
                  }
                  label="زمان‌بندی ارسال"
                />
              </Grid>

              {formData.isScheduled && (
                <Grid item xs={12}>
                  <DateTimePicker
                    label="زمان ارسال"
                    value={formData.scheduledAt || null}
                    onChange={date =>
                      setFormData(prev => ({
                        ...prev,
                        scheduledAt: date || undefined,
                      }))
                    }
                    slotProps={{
                      textField: {
                        fullWidth: true,
                      },
                    }}
                  />
                </Grid>
              )}

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="لینک عمل (اختیاری)"
                  value={formData.actionUrl || ''}
                  onChange={e =>
                    setFormData(prev => ({
                      ...prev,
                      actionUrl: e.target.value,
                    }))
                  }
                  placeholder="https://example.com/action"
                />
              </Grid>

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="لینک تصویر (اختیاری)"
                  value={formData.imageUrl || ''}
                  onChange={e =>
                    setFormData(prev => ({ ...prev, imageUrl: e.target.value }))
                  }
                  placeholder="https://example.com/image.jpg"
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setCreateDialog(false)}>انصراف</Button>
            <Button
              variant="contained"
              onClick={handleCreateNotification}
              disabled={!formData.title || !formData.message}
              startIcon={<SendIcon />}
            >
              ارسال اعلان
            </Button>
          </DialogActions>
        </Dialog>

        {/* Bulk Delete Dialog */}
        <Dialog
          open={bulkDeleteDialog}
          onClose={() => setBulkDeleteDialog(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>حذف گروهی اعلان‌ها</DialogTitle>
          <DialogContent>
            <Typography>
              آیا از حذف {selectedRows.length} اعلان اطمینان دارید؟
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setBulkDeleteDialog(false)}>انصراف</Button>
            <Button
              variant="contained"
              color="error"
              onClick={handleBulkDelete}
            >
              حذف
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </LocalizationProvider>
  );
}
