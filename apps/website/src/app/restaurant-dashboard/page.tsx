'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { useRouter } from 'next/navigation'
import apiService from '@/lib/api'
import OverviewTab from '@/components/dashboard/OverviewTab'
import OrdersTab from '@/components/dashboard/OrdersTab'
import MenuTab from '@/components/dashboard/MenuTab'
import AnalyticsTab from '@/components/dashboard/AnalyticsTab'
import SettingsTab from '@/components/dashboard/SettingsTab'
import RestaurantPhotoManager from '@/components/restaurant/RestaurantPhotoManager'
import { 
  ChartBarIcon,
  ClipboardDocumentListIcon,
  Cog6ToothIcon,
  BuildingStorefrontIcon,
  PresentationChartLineIcon,
  BellIcon,
  UserGroupIcon,
  CurrencyDollarIcon,
  PhotoIcon
} from '@heroicons/react/24/outline'

interface RestaurantStats {
  todayOrders: number
  todayRevenue: number
  totalCustomers: number
  averageRating: number
  pendingOrders: number
  totalMenuItems: number
  monthlyRevenue: number
  weeklyOrders: number
}

export default function RestaurantDashboard() {
  const [activeTab, setActiveTab] = useState('overview')
  const [loading, setLoading] = useState(true)
  const [stats, setStats] = useState<RestaurantStats>({
    todayOrders: 0,
    todayRevenue: 0,
    totalCustomers: 0,
    averageRating: 0,
    pendingOrders: 0,
    totalMenuItems: 0,
    monthlyRevenue: 0,
    weeklyOrders: 0
  })
  const [restaurant, setRestaurant] = useState<any>(null)
  const [notifications, setNotifications] = useState(0)
  const [pendingOrdersCount, setPendingOrdersCount] = useState(0)
  const [lastOrderCheck, setLastOrderCheck] = useState<Date>(new Date())
  const [showToast, setShowToast] = useState(false)
  const [toastMessage, setToastMessage] = useState('')
  const { user, isAuthenticated } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login')
      return
    }

    if (user?.role !== 'restaurant_owner') {
      router.push('/')
      return
    }

    loadDashboardData()
  }, [isAuthenticated, user])

  const loadDashboardData = async () => {
    try {
      setLoading(true)
      
      // Load restaurant data and stats
      const [restaurantData, statsData] = await Promise.all([
        apiService.getOwnerRestaurant(),
        apiService.getRestaurantStats()
      ])

      setRestaurant(restaurantData)
      setStats(statsData)
      
      // Load pending orders for notifications
      await checkForNewOrders(restaurantData.id)
    } catch (error) {
      console.error('Error loading dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  const checkForNewOrders = async (restaurantId?: string) => {
    try {
      if (!restaurantId && !restaurant?.id) return
      
      const orders = await apiService.getRestaurantOrders({
        restaurantId: restaurantId || restaurant.id
      })
      
      // Count pending orders
      const pendingOrders = orders.filter((order: any) => order.status === 'pending')
      const pendingCount = pendingOrders.length
      
      // Check for new orders since last check
      const newOrders = orders.filter((order: any) => 
        new Date(order.created_at) > lastOrderCheck && order.status === 'pending'
      )
      
      setPendingOrdersCount(pendingCount)
      
      // Show notification for new orders
      if (newOrders.length > 0) {
        setNotifications(prev => prev + newOrders.length)
        
        // Show toast message
        const message = newOrders.length === 1 
          ? `سفارش جدید دریافت شد!` 
          : `${newOrders.length} سفارش جدید دریافت شد!`
        setToastMessage(message)
        setShowToast(true)
        
        // Hide toast after 5 seconds
        setTimeout(() => setShowToast(false), 5000)
        
        // Play notification sound
        try {
          const audio = new Audio('/sounds/notification.mp3')
          audio.volume = 0.5
          audio.play().catch(() => {
            // Fallback: use system beep if audio file not available
            console.log('🔔 New order notification!')
          })
        } catch (error) {
          console.log('🔔 New order notification!')
        }
        
        // Show browser notification if permission granted
        if (Notification.permission === 'granted') {
          new Notification(`سفارش جدید!`, {
            body: message,
            icon: '/icon-192x192.png'
          })
        }
      }
      
      setLastOrderCheck(new Date())
    } catch (error) {
      console.error('Error checking for new orders:', error)
    }
  }

  // Request notification permission
  useEffect(() => {
    if (Notification.permission === 'default') {
      Notification.requestPermission()
    }
  }, [])

  // Set up polling for new orders every 30 seconds
  useEffect(() => {
    if (!restaurant?.id) return
    
    const interval = setInterval(() => {
      checkForNewOrders()
    }, 30000) // Check every 30 seconds
    
    return () => clearInterval(interval)
  }, [restaurant?.id, lastOrderCheck])

  const tabs = [
    {
      id: 'overview',
      name: 'نمای کلی',
      icon: ChartBarIcon
    },
    {
      id: 'orders',
      name: 'سفارش‌ها',
      icon: ClipboardDocumentListIcon
    },
    {
      id: 'menu',
      name: 'منوی غذا',
      icon: BuildingStorefrontIcon
    },
    {
      id: 'photos',
      name: 'تصاویر',
      icon: PhotoIcon
    },
    {
      id: 'analytics',
      name: 'آمار و گزارش',
      icon: PresentationChartLineIcon
    },
    {
      id: 'settings',
      name: 'تنظیمات',
      icon: Cog6ToothIcon
    }
  ]

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fa-AF', {
      style: 'currency',
      currency: 'AFN'
    }).format(amount || 0)
  }

  const safeNumber = (value: any): number => {
    return typeof value === 'number' ? value : 0
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/3"></div>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="h-32 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
              ))}
            </div>
            <div className="h-96 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                داشبورد رستوران
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                {restaurant?.name || 'مدیریت رستوران و سفارش‌ها'}
              </p>
            </div>
            
            <div className="flex items-center gap-4">
              {/* Quick Stats */}
              <div className="hidden md:flex items-center gap-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{safeNumber(stats.todayOrders)}</div>
                  <div className="text-xs text-gray-500">سفارش امروز</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{formatCurrency(safeNumber(stats.todayRevenue))}</div>
                  <div className="text-xs text-gray-500">درآمد امروز</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600">{pendingOrdersCount}</div>
                  <div className="text-xs text-gray-500">در انتظار</div>
                </div>
              </div>

              {/* Notifications */}
              <button 
                onClick={() => {
                  setNotifications(0)
                  setActiveTab('orders') // Navigate to orders tab
                }}
                className="relative p-2 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
                title={`${pendingOrdersCount} سفارش در انتظار`}
              >
                <BellIcon className="w-6 h-6" />
                {notifications > 0 && (
                  <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                    {notifications}
                  </span>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border border-gray-200 dark:border-gray-700">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
                <ClipboardDocumentListIcon className="w-6 h-6 text-green-600 dark:text-green-400" />
              </div>
              <div className="mr-4">
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  سفارش‌های امروز
                </h3>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{safeNumber(stats.todayOrders)}</p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border border-gray-200 dark:border-gray-700">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                <CurrencyDollarIcon className="w-6 h-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div className="mr-4">
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  درآمد امروز
                </h3>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{formatCurrency(safeNumber(stats.todayRevenue))}</p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border border-gray-200 dark:border-gray-700">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg flex items-center justify-center">
                <UserGroupIcon className="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
              </div>
              <div className="mr-4">
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  مشتریان
                </h3>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{safeNumber(stats.totalCustomers)}</p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border border-gray-200 dark:border-gray-700">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
                <ChartBarIcon className="w-6 h-6 text-purple-600 dark:text-purple-400" />
              </div>
              <div className="mr-4">
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  امتیاز میانگین
                </h3>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{safeNumber(stats.averageRating).toFixed(1)}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-6">
          <div className="border-b border-gray-200 dark:border-gray-700">
            <nav className="flex space-x-8 rtl:space-x-reverse px-6" aria-label="Tabs">
              {tabs.map((tab) => {
                const Icon = tab.icon
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2 transition-colors ${
                      activeTab === tab.id
                        ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                        : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300'
                    }`}
                  >
                    <Icon className="w-5 h-5" />
                    {tab.name}
                  </button>
                )
              })}
            </nav>
          </div>

          {/* Tab Content */}
          <div className="p-6">
            {activeTab === 'overview' && (
              <OverviewTab 
                stats={stats} 
                restaurant={restaurant}
                onRefresh={loadDashboardData}
              />
            )}

            {activeTab === 'orders' && (
              <OrdersTab 
                restaurant={restaurant}
                onOrderUpdate={() => {
                  loadDashboardData()
                  checkForNewOrders() // Refresh notifications when orders are updated
                }}
              />
            )}

            {activeTab === 'menu' && (
              <MenuTab 
                restaurant={restaurant}
                onMenuUpdate={loadDashboardData}
              />
            )}

            {activeTab === 'photos' && restaurant && (
              <RestaurantPhotoManager 
                restaurantId={restaurant.id}
                onPhotosUpdate={loadDashboardData}
              />
            )}

            {activeTab === 'analytics' && (
              <AnalyticsTab 
                restaurant={restaurant}
                stats={stats}
              />
            )}

            {activeTab === 'settings' && (
              <SettingsTab 
                restaurant={restaurant}
                onRestaurantUpdate={(updatedRestaurant) => {
                  setRestaurant(updatedRestaurant)
                  loadDashboardData()
                }}
              />
            )}
          </div>
        </div>
      </div>

      {/* Toast Notification */}
      {showToast && (
        <div className="fixed top-4 right-4 z-50 max-w-sm">
          <div 
            className="bg-green-600 text-white px-6 py-4 rounded-lg shadow-lg flex items-center space-x-3 rtl:space-x-reverse cursor-pointer hover:bg-green-700 transition-colors"
            onClick={() => {
              setShowToast(false)
              setActiveTab('orders')
            }}
          >
            <BellIcon className="w-6 h-6" />
            <div>
              <p className="font-medium">{toastMessage}</p>
              <p className="text-sm opacity-90">کلیک کنید تا سفارش‌ها را مشاهده کنید</p>
            </div>
            <button 
              onClick={(e) => {
                e.stopPropagation()
                setShowToast(false)
              }}
              className="text-white hover:text-gray-200"
            >
              ✕
            </button>
          </div>
        </div>
      )}
    </div>
  )
} 