# Development Guide - Next.js 15 + Turbopack + pnpm + Turborepo

This guide covers development workflows, best practices, and guidelines for the Azkuja Restaurant Directory using the modern tech stack with Turborepo for monorepo management.

## 🏗️ Architecture Overview

### Technology Stack
- **Frontend**: Next.js 15.1.3 + React 19.0.0 + TypeScript
- **Backend**: NestJS + TypeORM + PostgreSQL
- **Build Tool**: Turbopack (Next.js 15 integrated)
- **Package Manager**: pnpm 9.15.0
- **Monorepo Tool**: Turborepo 2.5.5
- **Authentication**: JWT with refresh tokens

### Directory Structure
```
apps/
├── website/          # Next.js 15 website
│   ├── src/
│   │   ├── app/      # App Router (Next.js 13+)
│   │   ├── components/
│   │   ├── contexts/ # React contexts (AuthContext)
│   │   ├── lib/      # Utilities (API service, auth manager)
│   │   └── styles/
│   ├── next.config.js
│   └── package.json
├── backend/          # NestJS API
├── admin/           # Admin dashboard
└── mobile/          # React Native app
packages/
└── shared/          # Shared utilities
```

## 🚀 Development Workflow

### Turborepo Commands
The project now uses Turborepo for efficient monorepo management with intelligent caching and parallel execution.

```bash
# Build all packages (respects dependencies)
pnpm build

# Build specific package
pnpm turbo build --filter=@azkuja/website

# Start development servers
pnpm dev                    # All packages in parallel
pnpm dev:website           # Website only (port 3000)
pnpm dev:backend           # Backend only (port 8000)
pnpm dev:admin             # Admin only (port 5050)

# Lint all packages
pnpm lint

# Lint with auto-fix
pnpm lint:fix

# Type checking
pnpm turbo type-check

# Clean all build artifacts
pnpm clean
```

### Starting Development
```bash
# Start all services in parallel (recommended)
pnpm dev

# Or start individually with Turborepo
pnpm turbo dev --filter=@azkuja/website
pnpm turbo start:dev --filter=@azkuja/backend
```

### Turborepo Features
- **Intelligent Caching**: Builds are cached and reused across the team
- **Parallel Execution**: Tasks run in parallel when possible
- **Task Pipeline**: Respects dependencies between packages
- **Incremental Builds**: Only rebuilds what changed
- **Remote Caching**: Ready for team-wide cache sharing (configurable)

### Turbopack Features (Next.js)
- **Fast Refresh**: Near-instant updates
- **Incremental Compilation**: Only recompiles changed files
- **Better Error Messages**: Enhanced debugging
- **CSS Hot Reloading**: Instant style updates

## 🏗️ Turborepo Task Pipeline

### Task Dependencies
The build pipeline is configured to respect package dependencies:

```
shared (build) → backend (build) → website (build)
                              → admin (build)
```

### Task Configuration
Tasks are defined in `turbo.json`:

```json
{
  "tasks": {
    "build": {
      "dependsOn": ["^build"],
      "outputs": ["dist/**", ".next/**", "build/**"]
    },
    "dev": {
      "cache": false,
      "persistent": true,
      "dependsOn": ["^build"]
    },
    "lint": {
      "dependsOn": ["^build"]
    }
  }
}
```

### Caching Strategy
- **Build outputs** are cached (dist/, .next/, build/)
- **Lint results** are cached
- **Type checking** is cached
- **Development servers** are not cached (persistent tasks)

### Cache Invalidation
Caches are invalidated when:
- Source files change
- Dependencies change
- Configuration files change (.env, next.config.js, etc.)
- Global dependencies change (package.json, turbo.json)

## 🔧 Next.js 15 Features & Best Practices

### App Router (Recommended)
```typescript
// app/page.tsx - Server Component by default
export default function HomePage() {
  return <div>Server-rendered content</div>
}

// app/client-component.tsx - Client Component
'use client'
export default function ClientComponent() {
  const [state, setState] = useState()
  return <div>Interactive content</div>
}
```

### Server Actions (Next.js 15)
```typescript
// app/actions.ts
'use server'
export async function createRestaurant(formData: FormData) {
  // Server-side logic
  const name = formData.get('name')
  // Database operations
}

// app/form.tsx
import { createRestaurant } from './actions'
export default function RestaurantForm() {
  return (
    <form action={createRestaurant}>
      <input name="name" />
      <button type="submit">Create</button>
    </form>
  )
}
```

### Image Optimization
```typescript
import Image from 'next/image'

// Optimized images with Next.js 15
<Image
  src="/restaurant.jpg"
  alt="Restaurant"
  width={800}
  height={600}
  priority // For above-the-fold images
/>
```

## ⚛️ React 19 Features

### New Hooks
```typescript
// useOptimistic - Optimistic updates
import { useOptimistic } from 'react'

function RestaurantList({ restaurants }) {
  const [optimisticRestaurants, addOptimistic] = useOptimistic(
    restaurants,
    (state, newRestaurant) => [...state, newRestaurant]
  )
  
  return (
    <div>
      {optimisticRestaurants.map(restaurant => (
        <RestaurantCard key={restaurant.id} restaurant={restaurant} />
      ))}
    </div>
  )
}
```

### Actions and Forms
```typescript
// React 19 form actions
function RestaurantForm() {
  async function submitAction(formData: FormData) {
    // Form submission logic
  }
  
  return (
    <form action={submitAction}>
      <input name="name" required />
      <button type="submit">Submit</button>
    </form>
  )
}
```

## 🔐 Authentication System

### Current Implementation
The authentication system uses a global state manager to prevent race conditions:

```typescript
// lib/auth-state-manager.ts
class AuthStateManager {
  private isChecking: boolean = false
  private checkPromise: Promise<AuthCheckResult> | null = null
  
  async checkAuthStatus(apiService: any): Promise<AuthCheckResult> {
    // Prevents multiple concurrent auth checks
    if (this.isChecking && this.checkPromise) {
      return this.checkPromise
    }
    // ... implementation
  }
}
```

### Usage in Components
```typescript
// contexts/AuthContext.tsx
import { authStateManager } from '@/lib/auth-state-manager'

export const AuthProvider = ({ children }) => {
  useEffect(() => {
    const checkAuth = async () => {
      const result = await authStateManager.checkAuthStatus(apiService)
      if (result.success) {
        setUser(result.user)
      }
    }
    checkAuth()
  }, [])
}
```

### Testing Authentication
```typescript
// Test rapid refresh scenarios
// Visit: http://localhost:3000/test-auth
// 1. Login with phone: **********, OTP: 123456
// 2. Rapidly refresh page (F5 multiple times)
// 3. User should remain logged in
```

## 📦 Package Management with pnpm

### Workspace Commands
```bash
# Install dependency to specific package
pnpm --filter @azkuja/website add react-query

# Run script in specific package
pnpm --filter @azkuja/backend start:dev

# Install dependencies for all packages
pnpm install

# Update dependencies
pnpm update

# Clean all node_modules
pnpm --filter '*' clean
```

### Adding New Packages
```bash
# Production dependency
pnpm --filter @azkuja/website add lodash

# Development dependency
pnpm --filter @azkuja/website add -D @types/lodash

# Workspace dependency (shared package)
pnpm --filter @azkuja/website add @azkuja/shared@workspace:*
```

## 🎨 Styling Guidelines

### Tailwind CSS (Current)
```typescript
// Component styling
<div className="bg-white rounded-lg shadow-lg p-6">
  <h1 className="text-2xl font-bold text-gray-900">
    Restaurant Name
  </h1>
</div>
```

### CSS Modules (Alternative)
```typescript
// styles/Restaurant.module.css
.card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

// Component
import styles from './Restaurant.module.css'
<div className={styles.card}>Content</div>
```

## 🧪 Testing Strategy

### Unit Testing
```bash
# Install testing dependencies
pnpm --filter @azkuja/website add -D jest @testing-library/react

# Run tests
pnpm --filter @azkuja/website test
```

### E2E Testing
```bash
# Install Playwright
pnpm --filter @azkuja/website add -D @playwright/test

# Run E2E tests
pnpm --filter @azkuja/website test:e2e
```

### Authentication Testing
```typescript
// __tests__/auth.test.ts
import { authStateManager } from '@/lib/auth-state-manager'

describe('Authentication', () => {
  it('should handle rapid refresh scenarios', async () => {
    // Test concurrent auth checks
    const promises = Array(5).fill(null).map(() => 
      authStateManager.checkAuthStatus(mockApiService)
    )
    const results = await Promise.all(promises)
    // All should return same result
    expect(results.every(r => r.success === results[0].success)).toBe(true)
  })
})
```

## 🚀 Performance Optimization

### Turbopack Optimizations
```javascript
// next.config.js
const nextConfig = {
  experimental: {
    turbo: {
      rules: {
        // Custom Turbopack rules
        '*.svg': {
          loaders: ['@svgr/webpack'],
          as: '*.js',
        },
      },
    },
  },
}
```

### Code Splitting
```typescript
// Dynamic imports for code splitting
import dynamic from 'next/dynamic'

const HeavyComponent = dynamic(() => import('./HeavyComponent'), {
  loading: () => <p>Loading...</p>,
})
```

### Image Optimization
```typescript
// next.config.js
const nextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'res.cloudinary.com',
      },
    ],
    formats: ['image/webp', 'image/avif'],
  },
}
```

## 🔍 Debugging

### Turborepo Debugging
```bash
# Verbose Turborepo output
pnpm turbo build --verbosity=2

# Force cache bypass
pnpm turbo build --force

# Dry run (see what would be executed)
pnpm turbo build --dry-run

# Show task graph
pnpm turbo build --graph

# Profile task execution
pnpm turbo build --profile=profile.json
```

### Turbopack Debugging
```bash
# Enable debug mode
TURBOPACK_DEBUG=1 pnpm dev:website

# Verbose logging
DEBUG=* pnpm dev:website
```

### Common Turborepo Issues

#### Cache Issues
```bash
# Clear Turborepo cache
pnpm turbo clean

# Clear all caches and rebuild
pnpm clean:all && pnpm install && pnpm build
```

#### Task Dependencies
```bash
# Check task dependency graph
pnpm turbo build --graph

# Run tasks in specific order
pnpm turbo build --filter=@azkuja/shared
pnpm turbo build --filter=@azkuja/website
```

#### Performance Issues
```bash
# Run with timing information
pnpm turbo build --profile=build-profile.json

# Analyze the profile
# Open build-profile.json in Chrome DevTools Performance tab
```

### React DevTools
- Install React DevTools browser extension
- Use React 19 features in DevTools
- Profile component renders

### Network Debugging
```typescript
// API debugging
const apiService = new ApiService()
apiService.enableDebugMode() // Custom debug logging
```

## 📋 Code Standards

### TypeScript
```typescript
// Use strict types
interface Restaurant {
  id: string
  name: string
  location: {
    lat: number
    lng: number
  }
}

// Avoid any types
const restaurants: Restaurant[] = await fetchRestaurants()
```

### Component Structure
```typescript
// Component template
interface Props {
  restaurant: Restaurant
  onUpdate?: (restaurant: Restaurant) => void
}

export default function RestaurantCard({ restaurant, onUpdate }: Props) {
  // Hooks first
  const [isEditing, setIsEditing] = useState(false)
  
  // Event handlers
  const handleEdit = () => setIsEditing(true)
  
  // Render
  return (
    <div className="restaurant-card">
      {/* JSX */}
    </div>
  )
}
```

## 🚢 Deployment

### Build Process
```bash
# Build for production
pnpm build:all

# Build specific app
pnpm build:website
```

### Environment Variables
```bash
# Production environment
NODE_ENV=production
NEXT_PUBLIC_API_URL=https://api.azkuja.com
```

## 🔄 Migration Notes

### From Previous Stack
- ✅ Authentication system preserved
- ✅ API endpoints unchanged
- ✅ Database schema maintained
- ✅ UI components compatible

### Breaking Changes Handled
- Updated React types to v19
- Fixed TypeScript configurations
- Resolved package dependencies
- Updated build scripts

## 📞 Support & Resources

### Documentation
- [Next.js 15 Docs](https://nextjs.org/docs)
- [React 19 Docs](https://react.dev/)
- [Turbopack Docs](https://turbo.build/pack)
- [pnpm Docs](https://pnpm.io/)

### Internal Resources
- [Setup Guide](../setup/)
- [API Documentation](../api/)
- Authentication test page: `/test-auth`
