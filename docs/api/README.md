# API Documentation - Azkuja Restaurant Directory

This document describes the backend API endpoints for the Azkuja Restaurant Directory.

## 🌐 Base URL

- **Development**: `http://localhost:8000`
- **Production**: `https://api.azkuja.com`

## 🔐 Authentication

The API uses JWT (JSON Web Tokens) for authentication with a refresh token mechanism.

### Token Types
- **Access Token**: Short-lived (15 minutes) for API requests
- **Refresh Token**: Long-lived (30 days) for token renewal

### Authentication Headers
```http
Authorization: Bearer <access_token>
```

## 📋 Authentication Endpoints

### POST /auth/login
Send OTP to phone number for login.

**Request:**
```json
{
  "phone_number": "0712345678"
}
```

**Response:**
```json
{
  "message": "OTP sent successfully",
  "expires_in": 300
}
```

### POST /auth/verify-otp
Verify OTP and get authentication tokens.

**Request:**
```json
{
  "phone_number": "0712345678",
  "otp_code": "123456"
}
```

**Response:**
```json
{
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "uuid",
    "name": "John Doe",
    "phone_number": "0712345678",
    "email": "<EMAIL>",
    "role": "customer",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### GET /auth/me
Get current authenticated user information.

**Headers:**
```http
Authorization: Bearer <access_token>
```

**Response:**
```json
{
  "id": "uuid",
  "name": "John Doe",
  "phone_number": "0712345678",
  "email": "<EMAIL>",
  "role": "customer",
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z"
}
```

### POST /auth/refresh
Refresh access token using refresh token.

**Request:**
```json
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Response:**
```json
{
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

### POST /auth/logout
Logout and invalidate tokens.

**Headers:**
```http
Authorization: Bearer <access_token>
```

**Response:**
```json
{
  "message": "Logged out successfully"
}
```

## 🏪 Restaurant Endpoints

### GET /restaurants
Get list of restaurants with pagination and filtering.

**Query Parameters:**
- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 10)
- `search` (string): Search by name or description
- `category` (string): Filter by category
- `location` (string): Filter by location
- `rating` (number): Minimum rating filter

**Response:**
```json
{
  "data": [
    {
      "id": "uuid",
      "name": "Restaurant Name",
      "description": "Restaurant description",
      "category": "Fast Food",
      "location": {
        "address": "123 Main St",
        "city": "Nairobi",
        "coordinates": {
          "lat": -1.2921,
          "lng": 36.8219
        }
      },
      "contact": {
        "phone": "0712345678",
        "email": "<EMAIL>"
      },
      "rating": 4.5,
      "images": ["url1", "url2"],
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  ],
  "meta": {
    "total": 100,
    "page": 1,
    "limit": 10,
    "totalPages": 10
  }
}
```

### GET /restaurants/:id
Get restaurant by ID.

**Response:**
```json
{
  "id": "uuid",
  "name": "Restaurant Name",
  "description": "Detailed description",
  "category": "Fast Food",
  "location": {
    "address": "123 Main St",
    "city": "Nairobi",
    "coordinates": {
      "lat": -1.2921,
      "lng": 36.8219
    }
  },
  "contact": {
    "phone": "0712345678",
    "email": "<EMAIL>",
    "website": "https://restaurant.com"
  },
  "hours": {
    "monday": "09:00-22:00",
    "tuesday": "09:00-22:00"
  },
  "rating": 4.5,
  "reviews_count": 150,
  "images": ["url1", "url2"],
  "menu": [
    {
      "id": "uuid",
      "name": "Burger",
      "price": 500,
      "description": "Delicious burger"
    }
  ],
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z"
}
```

### POST /restaurants
Create new restaurant (Admin only).

**Headers:**
```http
Authorization: Bearer <access_token>
Content-Type: application/json
```

**Request:**
```json
{
  "name": "New Restaurant",
  "description": "Restaurant description",
  "category": "Fast Food",
  "location": {
    "address": "123 Main St",
    "city": "Nairobi",
    "coordinates": {
      "lat": -1.2921,
      "lng": 36.8219
    }
  },
  "contact": {
    "phone": "0712345678",
    "email": "<EMAIL>"
  }
}
```

## 📝 Review Endpoints

### GET /restaurants/:id/reviews
Get reviews for a restaurant.

**Query Parameters:**
- `page` (number): Page number
- `limit` (number): Items per page
- `rating` (number): Filter by rating

**Response:**
```json
{
  "data": [
    {
      "id": "uuid",
      "user": {
        "id": "uuid",
        "name": "John Doe"
      },
      "rating": 5,
      "comment": "Great food!",
      "images": ["url1"],
      "created_at": "2024-01-01T00:00:00Z"
    }
  ],
  "meta": {
    "total": 50,
    "page": 1,
    "limit": 10,
    "totalPages": 5
  }
}
```

### POST /restaurants/:id/reviews
Create a review (Authenticated users only).

**Headers:**
```http
Authorization: Bearer <access_token>
Content-Type: multipart/form-data
```

**Request:**
```json
{
  "rating": 5,
  "comment": "Excellent service!",
  "images": ["file1", "file2"]
}
```

## 📊 Category Endpoints

### GET /categories
Get all restaurant categories.

**Response:**
```json
{
  "data": [
    {
      "id": "uuid",
      "name": "Fast Food",
      "description": "Quick service restaurants",
      "icon": "icon-url",
      "restaurant_count": 25
    }
  ]
}
```

## 👤 User Endpoints

### GET /users/profile/me
Get current user profile.

**Headers:**
```http
Authorization: Bearer <access_token>
```

**Response:**
```json
{
  "id": "uuid",
  "name": "John Doe",
  "phone_number": "0712345678",
  "email": "<EMAIL>",
  "role": "customer",
  "preferences": {
    "favorite_categories": ["Fast Food", "Italian"],
    "dietary_restrictions": ["vegetarian"]
  },
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z"
}
```

### PUT /users/profile/me
Update user profile.

**Headers:**
```http
Authorization: Bearer <access_token>
Content-Type: application/json
```

**Request:**
```json
{
  "name": "John Smith",
  "email": "<EMAIL>",
  "preferences": {
    "favorite_categories": ["Italian", "Chinese"],
    "dietary_restrictions": ["vegetarian"]
  }
}
```

## 🔍 Search Endpoints

### GET /search
Global search across restaurants, categories, and locations.

**Query Parameters:**
- `q` (string): Search query
- `type` (string): Search type (restaurants, categories, locations)
- `page` (number): Page number
- `limit` (number): Items per page

**Response:**
```json
{
  "data": {
    "restaurants": [...],
    "categories": [...],
    "locations": [...]
  },
  "meta": {
    "total": 25,
    "query": "pizza",
    "type": "all"
  }
}
```

## ❌ Error Responses

### Standard Error Format
```json
{
  "statusCode": 400,
  "message": "Validation failed",
  "error": "Bad Request",
  "details": [
    {
      "field": "phone_number",
      "message": "Phone number is required"
    }
  ]
}
```

### Common HTTP Status Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Validation Error
- `500` - Internal Server Error

## 🔧 Development Tools

### Swagger Documentation
Visit `http://localhost:8000/api` for interactive API documentation.

### Testing with cURL
```bash
# Login
curl -X POST http://localhost:8000/auth/login \
  -H "Content-Type: application/json" \
  -d '{"phone_number": "0712345678"}'

# Get restaurants
curl -X GET http://localhost:8000/restaurants \
  -H "Authorization: Bearer <token>"
```

### Postman Collection
Import the Postman collection for easy API testing:
- Collection URL: `/docs/api/postman-collection.json`

## 📞 Support

For API-related issues:
1. Check the Swagger documentation at `/api`
2. Review error messages and status codes
3. Consult the development team
4. Check server logs for detailed error information
