# Extracting Mobile App to Separate Repository

## Step 1: Create a new repository
Create a new repository in GitHub/GitLab/etc. named "azkuja-mobile"

## Step 2: Copy the mobile package files
1. Copy the entire contents of `packages/mobile` to a new local directory
2. Create a new package.json for the standalone mobile repository:

```json
{
  "name": "azkuja-mobile",
  "version": "1.0.0",
  "main": "node_modules/expo/AppEntry.js",
  "scripts": {
    "start": "expo start",
    "android": "expo start --android",
    "ios": "expo start --ios",
    "web": "expo start --web",
    "lint": "eslint ."
  },
  "dependencies": {
    "@react-navigation/bottom-tabs": "^6.5.11",
    "@react-navigation/native": "^6.1.9",
    "@react-navigation/native-stack": "^6.9.17",
    "@reduxjs/toolkit": "^2.0.1",
    "axios": "^1.6.2",
    "expo": "~49.0.15",
    "expo-location": "~16.1.0",
    "expo-status-bar": "~1.6.0",
    "react": "18.2.0",
    "react-native": "0.72.6",
    "react-native-maps": "1.7.1",
    "react-native-safe-area-context": "4.6.3",
    "react-native-screens": "~3.22.0",
    "react-query": "^3.39.3",
    "react-redux": "^9.0.4"
  },
  "devDependencies": {
    "@babel/core": "^7.20.0",
    "@types/react": "~18.2.14",
    "typescript": "^5.1.3",
    "eslint": "^8.42.0",
    "eslint-config-prettier": "^9.0.0"
  },
  "private": true
}
```

## Step 3: Copy the shared types
Copy the types you need from the `packages/shared/src/types` directory into a new `src/types` directory in your mobile project.

## Step 4: Update imports
Update all imports from `@azkuja/shared` to the local types:

```typescript
// Before
import { Restaurant, Category } from '@azkuja/shared';

// After
import { Restaurant, Category } from '../types';
```

## Step 5: Update API configuration
Update the API URL in `src/services/api.ts` to point to your deployed backend:

```typescript
// Update this line
const API_URL = 'https://your-backend-url.com/api';
```

## Step 6: Initialize local Git repository
```bash
git init
git add .
git commit -m "Initial commit"
git remote add origin https://github.com/yourusername/azkuja-mobile.git
git push -u origin main
``` 