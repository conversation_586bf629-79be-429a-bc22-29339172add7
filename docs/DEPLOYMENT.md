# 🚀 Azkuja Restaurant Directory - Deployment Guide

This guide covers deploying the Azkuja Restaurant Directory application on two platforms:
- **Hostinger VPS** (Traditional VPS deployment)
- **Coolify** (Modern self-hosted PaaS)

## 📋 Pre-Deployment Checklist

Before deploying, ensure you have:
- [ ] Domain name configured
- [ ] SSL certificate ready
- [ ] Production environment variables prepared
- [ ] Database backup strategy planned
- [ ] Monitoring setup planned

---

# 🏢 Hostinger VPS Deployment

## Step 1: VPS Setup and Initial Configuration

### 1.1 Create and Access VPS
```bash
# SSH into your Hostinger VPS
ssh root@your-server-ip

# Update system packages
apt update && apt upgrade -y

# Install essential packages
apt install -y curl wget git htop nano ufw
```

### 1.2 Install Node.js and Yarn
```bash
# Install Node.js 18.x
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
apt install -y nodejs

# Install Yarn
npm install -g yarn

# Verify installations
node --version
yarn --version
```

### 1.3 Install Docker and Docker Compose
```bash
# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh

# Install Docker Compose
curl -L "https://github.com/docker/compose/releases/download/v2.23.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose

# Start Docker service
systemctl start docker
systemctl enable docker

# Verify installation
docker --version
docker-compose --version
```

### 1.4 Install Nginx
```bash
# Install Nginx
apt install -y nginx

# Start and enable Nginx
systemctl start nginx
systemctl enable nginx
```

### 1.5 Configure Firewall
```bash
# Configure UFW firewall
ufw allow OpenSSH
ufw allow 'Nginx Full'
ufw allow 80
ufw allow 443
ufw enable
```

## Step 2: Application Deployment

### 2.1 Clone and Setup Application
```bash
# Create application directory
mkdir -p /var/www/azkuja
cd /var/www/azkuja

# Clone repository
git clone https://github.com/hrkhavarie/Azkuja.git .

# Install dependencies
yarn install
```

### 2.2 Create Production Environment Files

#### Backend Environment
```bash
# Create backend production environment
cat > packages/backend/.env << EOF
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USER=azkuja_user
DB_PASSWORD=your_secure_password_here
DB_NAME=azkuja_prod

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379

# Application Configuration
NODE_ENV=production
PORT=7000
API_PREFIX=api

# JWT Configuration
JWT_SECRET=your-super-secure-jwt-secret-for-production
JWT_EXPIRES_IN=7d

# CORS Configuration
CORS_ORIGIN=https://azkuja.com,https://admin.azkuja.com

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_DEST=/var/www/azkuja/uploads

# Email Configuration
SMTP_HOST=smtp.hostinger.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-email-password

# SSL Configuration
SSL_CERT_PATH=/etc/letsencrypt/live/azkuja.com/fullchain.pem
SSL_KEY_PATH=/etc/letsencrypt/live/azkuja.com/privkey.pem
EOF
```

#### Website Environment
```bash
# Create website production environment
cat > packages/website/.env.local << EOF
# API Configuration
NEXT_PUBLIC_API_URL=https://api.azkuja.com/api
NEXT_PUBLIC_WS_URL=wss://api.azkuja.com

# Google Maps API
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your-google-maps-api-key

# Application Configuration
NODE_ENV=production
NEXT_PUBLIC_APP_NAME=Azkuja
EOF
```

#### Admin Environment
```bash
# Create admin production environment
cat > packages/admin/.env.local << EOF
# API Configuration
NEXT_PUBLIC_API_URL=https://api.azkuja.com/api

# Application Configuration
NODE_ENV=production
NEXT_PUBLIC_APP_NAME=Azkuja Admin

# Authentication
NEXTAUTH_URL=https://admin.azkuja.com
NEXTAUTH_SECRET=your-nextauth-secret-for-production
EOF
```

### 2.3 Setup Production Database
```bash
# Create production docker-compose file
cat > docker-compose.prod.yml << EOF
version: '3.8'

services:
  postgres:
    image: postgres:14-alpine
    container_name: azkuja-postgres-prod
    ports:
      - "5432:5432"
    environment:
      POSTGRES_USER: azkuja_user
      POSTGRES_PASSWORD: your_secure_password_here
      POSTGRES_DB: azkuja_prod
    volumes:
      - postgres_prod_data:/var/lib/postgresql/data
      - ./backups:/backups
    restart: unless-stopped
    networks:
      - azkuja-network

  redis:
    image: redis:7-alpine
    container_name: azkuja-redis-prod
    ports:
      - "6379:6379"
    volumes:
      - redis_prod_data:/data
    restart: unless-stopped
    networks:
      - azkuja-network

volumes:
  postgres_prod_data:
  redis_prod_data:

networks:
  azkuja-network:
    driver: bridge
EOF

# Start production database services
docker-compose -f docker-compose.prod.yml up -d
```

### 2.4 Build Applications
```bash
# Build shared package
cd /var/www/azkuja
yarn shared build

# Build backend
yarn build:backend

# Build website
yarn build:website

# Build admin
yarn build:admin
```

## Step 3: Process Management with PM2

### 3.1 Install and Configure PM2
```bash
# Install PM2 globally
npm install -g pm2

# Create PM2 ecosystem file
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [
    {
      name: 'azkuja-backend',
      cwd: '/var/www/azkuja/packages/backend',
      script: 'dist/main.js',
      instances: 2,
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        PORT: 7000
      },
      error_file: '/var/log/azkuja/backend-error.log',
      out_file: '/var/log/azkuja/backend-out.log',
      log_file: '/var/log/azkuja/backend.log'
    },
    {
      name: 'azkuja-website',
      cwd: '/var/www/azkuja/packages/website',
      script: 'node_modules/next/dist/bin/next',
      args: 'start -p 3000',
      instances: 1,
      env: {
        NODE_ENV: 'production',
        PORT: 3000
      },
      error_file: '/var/log/azkuja/website-error.log',
      out_file: '/var/log/azkuja/website-out.log',
      log_file: '/var/log/azkuja/website.log'
    },
    {
      name: 'azkuja-admin',
      cwd: '/var/www/azkuja/packages/admin',
      script: 'node_modules/next/dist/bin/next',
      args: 'start -p 5000',
      instances: 1,
      env: {
        NODE_ENV: 'production',
        PORT: 5000
      },
      error_file: '/var/log/azkuja/admin-error.log',
      out_file: '/var/log/azkuja/admin-out.log',
      log_file: '/var/log/azkuja/admin.log'
    }
  ]
};
EOF

# Create log directory
mkdir -p /var/log/azkuja

# Start applications
pm2 start ecosystem.config.js

# Save PM2 configuration
pm2 save

# Setup PM2 startup
pm2 startup
# Follow the instructions provided by the command above
```

## Step 4: Nginx Configuration

### 4.1 Create Nginx Configuration
```bash
# Create main site configuration
cat > /etc/nginx/sites-available/azkuja << EOF
# Backend API
server {
    listen 80;
    server_name api.azkuja.com;
    
    location / {
        proxy_pass http://localhost:7000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }
}

# Customer Website
server {
    listen 80;
    server_name azkuja.com www.azkuja.com;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }
}

# Admin Panel
server {
    listen 80;
    server_name admin.azkuja.com;
    
    location / {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }
}
EOF

# Enable the site
ln -s /etc/nginx/sites-available/azkuja /etc/nginx/sites-enabled/

# Test Nginx configuration
nginx -t

# Reload Nginx
systemctl reload nginx
```

### 4.2 SSL Certificate with Let's Encrypt
```bash
# Install Certbot
apt install -y certbot python3-certbot-nginx

# Obtain SSL certificates
certbot --nginx -d azkuja.com -d www.azkuja.com -d admin.azkuja.com -d api.azkuja.com

# Test automatic renewal
certbot renew --dry-run
```

## Step 5: Monitoring and Maintenance

### 5.1 Setup Log Rotation
```bash
# Create logrotate configuration
cat > /etc/logrotate.d/azkuja << EOF
/var/log/azkuja/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 root root
    postrotate
        pm2 reloadLogs
    endscript
}
EOF
```

### 5.2 Database Backup Script
```bash
# Create backup script
cat > /usr/local/bin/backup-azkuja-db.sh << EOF
#!/bin/bash
BACKUP_DIR="/var/www/azkuja/backups"
DATE=\$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="azkuja_backup_\$DATE.sql"

mkdir -p \$BACKUP_DIR

docker exec azkuja-postgres-prod pg_dump -U azkuja_user azkuja_prod > \$BACKUP_DIR/\$BACKUP_FILE

# Keep only last 7 days of backups
find \$BACKUP_DIR -name "azkuja_backup_*.sql" -mtime +7 -delete

echo "Backup completed: \$BACKUP_FILE"
EOF

chmod +x /usr/local/bin/backup-azkuja-db.sh

# Add to crontab for daily backups
echo "0 2 * * * /usr/local/bin/backup-azkuja-db.sh" | crontab -
```

---

# ☁️ Coolify Deployment

Coolify is a modern, self-hosted alternative to Heroku/Netlify that makes deployment much easier.

## Step 1: Coolify Server Setup

### 1.1 Install Coolify
```bash
# SSH into your server
ssh root@your-server-ip

# Install Coolify (one command installation)
curl -fsSL https://cdn.coollabs.io/coolify/install.sh | bash
```

### 1.2 Access Coolify Dashboard
1. Open your browser and go to `http://your-server-ip:8000`
2. Complete the initial setup
3. Create your admin account

## Step 2: Project Setup in Coolify

### 2.1 Create New Project
1. **Login to Coolify Dashboard**
2. **Click "New Project"**
3. **Project Name**: `azkuja-restaurant-directory`
4. **Description**: `Restaurant Directory Application`

### 2.2 Add Git Repository
1. **Go to Sources** → **Add New Source**
2. **Source Type**: GitHub/GitLab
3. **Repository URL**: `https://github.com/hrkhavarie/Azkuja.git`
4. **Branch**: `main`

## Step 3: Database Setup

### 3.1 Create PostgreSQL Database
1. **Go to Databases** → **Add New Database**
2. **Database Type**: PostgreSQL
3. **Database Name**: `azkuja-postgres`
4. **Username**: `azkuja_user`
5. **Password**: Generate secure password
6. **Database Name**: `azkuja_prod`
7. **Click Deploy**

### 3.2 Create Redis Database
1. **Add New Database** → **Redis**
2. **Database Name**: `azkuja-redis`
3. **Click Deploy**

## Step 4: Backend API Deployment

### 4.1 Create Backend Service
1. **Go to Applications** → **Add New Application**
2. **Application Type**: Node.js
3. **Source**: Select your repository
4. **Build Pack**: Node.js
5. **Root Directory**: `packages/backend`
6. **Build Command**: `yarn install && yarn build`
7. **Start Command**: `node dist/main.js`
8. **Port**: `7000`

### 4.2 Backend Environment Variables
Add these environment variables in Coolify:

```env
NODE_ENV=production
PORT=7000
API_PREFIX=api

# Database (use Coolify's internal URLs)
DB_HOST=azkuja-postgres
DB_PORT=5432
DB_USER=azkuja_user
DB_PASSWORD=your_generated_password
DB_NAME=azkuja_prod

# Redis
REDIS_HOST=azkuja-redis
REDIS_PORT=6379

# JWT
JWT_SECRET=your-super-secure-jwt-secret
JWT_EXPIRES_IN=7d

# CORS
CORS_ORIGIN=https://azkuja.com,https://admin.azkuja.com

# File Upload
MAX_FILE_SIZE=10485760
UPLOAD_DEST=/app/uploads
```

### 4.3 Deploy Backend
1. **Click Deploy**
2. **Wait for deployment to complete**
3. **Check logs for any errors**

## Step 5: Website Deployment

### 5.1 Create Website Service
1. **Add New Application**
2. **Application Type**: Node.js
3. **Root Directory**: `packages/website`
4. **Build Command**: `yarn install && yarn build`
5. **Start Command**: `yarn start`
6. **Port**: `3000`

### 5.2 Website Environment Variables
```env
NODE_ENV=production
NEXT_PUBLIC_API_URL=https://api.azkuja.com/api
NEXT_PUBLIC_WS_URL=wss://api.azkuja.com
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your-google-maps-api-key
NEXT_PUBLIC_APP_NAME=Azkuja
```

## Step 6: Admin Panel Deployment

### 6.1 Create Admin Service
1. **Add New Application**
2. **Root Directory**: `packages/admin`
3. **Build Command**: `yarn install && yarn build`
4. **Start Command**: `yarn start`
5. **Port**: `5000`

### 6.2 Admin Environment Variables
```env
NODE_ENV=production
NEXT_PUBLIC_API_URL=https://api.azkuja.com/api
NEXT_PUBLIC_APP_NAME=Azkuja Admin
NEXTAUTH_URL=https://admin.azkuja.com
NEXTAUTH_SECRET=your-nextauth-secret
```

## Step 7: Domain Configuration

### 7.1 Configure Domains
1. **Backend API**: 
   - Domain: `api.azkuja.com`
   - Port: `7000`
   - Enable SSL

2. **Website**:
   - Domain: `azkuja.com`
   - Port: `3000`
   - Enable SSL

3. **Admin Panel**:
   - Domain: `admin.azkuja.com`
   - Port: `5000`
   - Enable SSL

### 7.2 SSL Certificates
Coolify automatically handles SSL certificates with Let's Encrypt.

## Step 8: Monitoring and Logs

### 8.1 Application Monitoring
1. **Go to each application**
2. **Check "Metrics" tab** for performance
3. **Monitor "Logs" tab** for errors
4. **Set up alerts** in Settings

### 8.2 Database Monitoring
1. **Go to Databases**
2. **Check metrics** for each database
3. **Monitor connection counts**
4. **Set up automated backups**

---

# 🔧 Post-Deployment Configuration

## Mobile App Configuration

### Update API URLs
```bash
# Update mobile app environment for production
cat > packages/mobile/.env << EOF
EXPO_PUBLIC_API_URL=https://api.azkuja.com/api
EXPO_PUBLIC_APP_NAME=Azkuja
EXPO_PUBLIC_APP_VERSION=1.0.0
EOF
```

### Build and Deploy Mobile App
```bash
# Install Expo CLI
npm install -g @expo/cli

# Login to Expo
expo login

# Build for production
cd packages/mobile
expo build:android
expo build:ios
```

## DNS Configuration

Configure your domain DNS records for **Azkuja.com**:

```
Type    Name    Value                   TTL
A       @       your-server-ip          300
A       www     your-server-ip          300
A       admin   your-server-ip          300
A       api     your-server-ip          300
```

## Security Checklist

- [ ] Change all default passwords
- [ ] Enable firewall (UFW for Hostinger)
- [ ] Configure SSL certificates
- [ ] Set up fail2ban for SSH protection
- [ ] Enable database connection encryption
- [ ] Configure CORS properly
- [ ] Set up rate limiting
- [ ] Enable application logging
- [ ] Configure backup strategies

## Performance Optimization

### Hostinger VPS
- Use PM2 cluster mode for backend
- Configure Nginx caching
- Set up CDN for static assets
- Optimize database queries
- Enable Gzip compression

### Coolify
- Enable auto-scaling if needed
- Configure resource limits
- Set up horizontal scaling
- Monitor resource usage
- Optimize build processes

---

# 🚨 Troubleshooting

## Common Issues

### Database Connection Failed
```bash
# Check database status
docker ps
docker logs azkuja-postgres-prod

# Test connection
docker exec -it azkuja-postgres-prod psql -U azkuja_user -d azkuja_prod
```

### Application Won't Start
```bash
# Check PM2 logs (Hostinger)
pm2 logs

# Check Coolify logs
# Go to application → Logs tab
```

### SSL Certificate Issues
```bash
# Renew certificates (Hostinger)
certbot renew

# Check certificate status
certbot certificates
```

### Performance Issues
```bash
# Monitor system resources
htop
docker stats

# Check application metrics
pm2 monit  # For Hostinger
# Use Coolify dashboard for Coolify deployment
```

---

# 📊 Monitoring and Maintenance

## Regular Maintenance Tasks

### Daily
- [ ] Check application logs
- [ ] Monitor system resources
- [ ] Verify backup completion

### Weekly
- [ ] Update system packages
- [ ] Review performance metrics
- [ ] Check SSL certificate expiry

### Monthly
- [ ] Update application dependencies
- [ ] Review security logs
- [ ] Optimize database performance
- [ ] Clean up old logs and backups

## Backup Strategy

### Database Backups
- **Frequency**: Daily at 2 AM
- **Retention**: 30 days
- **Location**: Local and cloud storage
- **Testing**: Weekly restore tests

### Application Backups
- **Code**: Git repository
- **Uploads**: Daily sync to cloud storage
- **Configuration**: Version controlled

---

## 🌐 **Your Azkuja.com URLs After Deployment**

Once deployment is complete, your application will be accessible at:

### **Production URLs:**
- **🏠 Customer Website**: https://azkuja.com
- **👨‍💼 Admin Panel**: https://admin.azkuja.com  
- **🔌 Backend API**: https://api.azkuja.com/api
- **📚 API Documentation**: https://api.azkuja.com/api/docs

### **DNS Configuration Required:**
Make sure to configure these DNS A records in your domain registrar:
```
Type    Name    Points to              TTL
A       @       your-server-ip         300
A       www     your-server-ip         300  
A       admin   your-server-ip         300
A       api     your-server-ip         300
```

### **SSL Certificates:**
All domains will be secured with Let's Encrypt SSL certificates automatically.

---

**Congratulations! Your Azkuja Restaurant Directory is now deployed and ready for production use! 🎉**

For support and updates, refer to the main README.md file or create an issue in the GitHub repository. 