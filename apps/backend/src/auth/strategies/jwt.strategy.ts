import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UsersService } from '../../users/users.service';
import { UserSession } from '../entities/user-session.entity';
import { User } from '../../users/entities/user.entity';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(
    private readonly configService: ConfigService,
    private readonly usersService: UsersService,
    @InjectRepository(UserSession)
    private readonly userSessionRepository: Repository<UserSession>,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: configService.get('JWT_SECRET') || 'your-secret-key',
    });
  }

  async validate(payload: any): Promise<User> {
    try {
      console.log('JWT Strategy - Validating payload:', {
        sub: payload.sub,
        email: payload.email,
        iat: payload.iat,
        exp: payload.exp,
      });

      const { sub: userId } = payload;

      if (!userId) {
        console.log('JWT Strategy - No user ID in payload');
        throw new UnauthorizedException('Invalid token: no user ID');
      }

      // Get complete user information
      const user = await this.usersService.findOne(userId);
      if (!user) {
        console.log('JWT Strategy - User not found:', userId);
        throw new UnauthorizedException('Invalid token: user not found');
      }

      console.log('JWT Strategy - User found:', {
        id: user.id,
        name: user.name,
        role: user.role,
        email: user.email,
      });

      // Validate session exists and is active
      try {
        const session = await this.userSessionRepository.findOne({
          where: {
            user: { id: userId },
          },
        });

        if (!session) {
          console.log('JWT Strategy - No session found for user:', userId);
          throw new UnauthorizedException(
            'Session not found. Please login again.',
          );
        }

        if (session.expires_at <= new Date()) {
          console.log('JWT Strategy - Session expired for user:', userId);
          throw new UnauthorizedException(
            'Session expired. Please login again.',
          );
        }

        // Update session activity
        session.last_activity = new Date();
        await this.userSessionRepository.save(session);
        console.log(
          'JWT Strategy - Session validated and updated for user:',
          userId,
        );
      } catch (sessionError) {
        console.error(
          'JWT Strategy - Session validation failed:',
          sessionError.message,
        );
        if (sessionError instanceof UnauthorizedException) {
          throw sessionError;
        }
        throw new UnauthorizedException('Session validation failed');
      }

      // Return the complete user object
      return user;
    } catch (error) {
      console.error('JWT Strategy - Validation error:', {
        message: error.message,
        payload: payload,
      });
      throw new UnauthorizedException('Token validation failed');
    }
  }
}
