'use client';

import React, { useState, useEffect } from 'react';
import { useList } from '@refinedev/core';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Stack,
  LinearProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Paper,
  Divider,
  Alert,
  Chip,
  Avatar,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Restaurant as RestaurantIcon,
  People as PeopleIcon,
  Receipt as OrderIcon,
  AttachMoney as RevenueIcon,
  Star as ReviewIcon,
  Notifications as NotificationIcon,
  Schedule as ScheduleIcon,
  LocalShipping as DeliveryIcon,
  Assessment as AnalyticsIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

interface DashboardStats {
  overview: {
    totalRestaurants: number;
    activeRestaurants: number;
    totalUsers: number;
    totalOrders: number;
    totalRevenue: number;
    avgOrderValue: number;
    totalReviews: number;
    avgRating: number;
  };
  growth: {
    restaurants: number;
    users: number;
    orders: number;
    revenue: number;
  };
  orderStats: {
    pending: number;
    confirmed: number;
    preparing: number;
    ready: number;
    delivered: number;
    cancelled: number;
  };
  revenueChart: Array<{
    date: string;
    revenue: number;
    orders: number;
  }>;
  topRestaurants: Array<{
    id: string;
    name: string;
    city: string;
    orders: number;
    revenue: number;
    rating: number;
  }>;
  userActivity: Array<{
    hour: number;
    users: number;
    orders: number;
  }>;
  categoryDistribution: Array<{
    name: string;
    value: number;
    color: string;
  }>;
}

const COLORS = [
  '#8884d8',
  '#82ca9d',
  '#ffc658',
  '#ff7300',
  '#8dd1e1',
  '#d084d0',
];

export default function AnalyticsDashboard() {
  const [dateRange, setDateRange] = useState('7days');
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());

  // Fetch data from multiple sources
  const {
    data: restaurantsData,
    isLoading: restaurantsLoading,
    refetch: refetchRestaurants,
  } = useList({
    resource: 'restaurants',
    pagination: { mode: 'off' },
  });

  const {
    data: usersData,
    isLoading: usersLoading,
    refetch: refetchUsers,
  } = useList({
    resource: 'users',
    pagination: { mode: 'off' },
  });

  const {
    data: ordersData,
    isLoading: ordersLoading,
    refetch: refetchOrders,
  } = useList({
    resource: 'orders',
    pagination: { mode: 'off' },
    filters: [
      {
        field: 'created_at',
        operator: 'gte',
        value: getDateRangeStart(dateRange),
      },
    ],
  });

  const {
    data: reviewsData,
    isLoading: reviewsLoading,
    refetch: refetchReviews,
  } = useList({
    resource: 'reviews',
    pagination: { mode: 'off' },
    filters: [
      {
        field: 'created_at',
        operator: 'gte',
        value: getDateRangeStart(dateRange),
      },
    ],
  });

  const { data: analyticsData, isLoading: analyticsLoading } = useList({
    resource: 'analytics/dashboard',
    pagination: { mode: 'off' },
    filters: [
      {
        field: 'period',
        operator: 'eq',
        value: dateRange,
      },
    ],
  });

  function getDateRangeStart(range: string): string {
    const now = new Date();
    switch (range) {
      case '24hours':
        return new Date(now.getTime() - 24 * 60 * 60 * 1000).toISOString();
      case '7days':
        return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString();
      case '30days':
        return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000).toISOString();
      case '90days':
        return new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000).toISOString();
      default:
        return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString();
    }
  }

  useEffect(() => {
    if (
      !restaurantsLoading &&
      !usersLoading &&
      !ordersLoading &&
      !reviewsLoading &&
      !analyticsLoading
    ) {
      calculateStats();
    }
  }, [
    restaurantsData,
    usersData,
    ordersData,
    reviewsData,
    analyticsData,
    restaurantsLoading,
    usersLoading,
    ordersLoading,
    reviewsLoading,
    analyticsLoading,
  ]);

  const calculateStats = () => {
    try {
      const restaurants = restaurantsData?.data || [];
      const users = usersData?.data || [];
      const orders = ordersData?.data || [];
      const reviews = reviewsData?.data || [];
      const analytics = analyticsData?.data || {};

      // Calculate overview stats
      const totalRevenue = orders.reduce(
        (sum: number, order: any) => sum + (Number(order.total) || 0),
        0
      );
      const avgOrderValue =
        orders.length > 0 ? totalRevenue / orders.length : 0;
      const avgRating =
        reviews.length > 0
          ? reviews.reduce(
              (sum: number, review: any) => sum + (Number(review.rating) || 0),
              0
            ) / reviews.length
          : 0;

      // Calculate order status distribution
      const orderStats = {
        pending: orders.filter((o: any) => o.status === 'pending').length,
        confirmed: orders.filter((o: any) => o.status === 'confirmed').length,
        preparing: orders.filter((o: any) => o.status === 'preparing').length,
        ready: orders.filter((o: any) => o.status === 'ready').length,
        delivered: orders.filter((o: any) => o.status === 'delivered').length,
        cancelled: orders.filter((o: any) => o.status === 'cancelled').length,
      };

      // Generate revenue chart data
      const revenueChart = generateRevenueChartData(orders, dateRange);

      // Calculate top restaurants
      const restaurantOrderMap = new Map();
      orders.forEach((order: any) => {
        const restaurantId = order.restaurant_id;
        if (!restaurantOrderMap.has(restaurantId)) {
          restaurantOrderMap.set(restaurantId, { orders: 0, revenue: 0 });
        }
        const current = restaurantOrderMap.get(restaurantId);
        current.orders += 1;
        current.revenue += Number(order.total) || 0;
      });

      const topRestaurants = restaurants
        .map((restaurant: any) => {
          const stats = restaurantOrderMap.get(restaurant.id) || {
            orders: 0,
            revenue: 0,
          };
          return {
            id: restaurant.id,
            name: restaurant.name,
            city: restaurant.city,
            orders: stats.orders,
            revenue: stats.revenue,
            rating: restaurant.avg_rating || 0,
          };
        })
        .sort((a: any, b: any) => b.revenue - a.revenue)
        .slice(0, 5);

      // Generate user activity data
      const userActivity = generateUserActivityData(orders);

      // Generate category distribution
      const categoryDistribution = generateCategoryDistribution(restaurants);

      const dashboardStats: DashboardStats = {
        overview: {
          totalRestaurants: restaurants.length,
          activeRestaurants: restaurants.filter(
            (r: any) => r.status === 'active'
          ).length,
          totalUsers: users.length,
          totalOrders: orders.length,
          totalRevenue,
          avgOrderValue,
          totalReviews: reviews.length,
          avgRating,
        },
        growth: {
          restaurants: 12.5, // Mock growth data
          users: 8.3,
          orders: 15.7,
          revenue: 23.1,
        },
        orderStats,
        revenueChart,
        topRestaurants,
        userActivity,
        categoryDistribution,
      };

      setStats(dashboardStats);
      setLastUpdated(new Date());
    } catch (error) {
      console.error('Error calculating dashboard stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const generateRevenueChartData = (orders: any[], range: string) => {
    const data = [];
    const days =
      range === '24hours'
        ? 24
        : range === '7days'
          ? 7
          : range === '30days'
            ? 30
            : 90;
    const isHourly = range === '24hours';

    for (let i = days - 1; i >= 0; i--) {
      const date = new Date();
      if (isHourly) {
        date.setHours(date.getHours() - i);
      } else {
        date.setDate(date.getDate() - i);
      }

      const dayOrders = orders.filter((order: any) => {
        const orderDate = new Date(order.created_at);
        if (isHourly) {
          return (
            orderDate.getHours() === date.getHours() &&
            orderDate.toDateString() === date.toDateString()
          );
        } else {
          return orderDate.toDateString() === date.toDateString();
        }
      });

      data.push({
        date: isHourly
          ? date.toLocaleTimeString('fa-IR', {
              hour: '2-digit',
              minute: '2-digit',
            })
          : date.toLocaleDateString('fa-IR', {
              month: 'short',
              day: 'numeric',
            }),
        revenue: dayOrders.reduce(
          (sum: number, order: any) => sum + (Number(order.total) || 0),
          0
        ),
        orders: dayOrders.length,
      });
    }

    return data;
  };

  const generateUserActivityData = (orders: any[]) => {
    const hourlyData = Array.from({ length: 24 }, (_, hour) => ({
      hour,
      users: 0,
      orders: 0,
    }));

    orders.forEach((order: any) => {
      const hour = new Date(order.created_at).getHours();
      hourlyData[hour].orders += 1;
      hourlyData[hour].users += 1; // Simplified - in real app, count unique users
    });

    return hourlyData;
  };

  const generateCategoryDistribution = (restaurants: any[]) => {
    const categoryMap = new Map();

    restaurants.forEach((restaurant: any) => {
      const category = restaurant.category || 'سایر';
      categoryMap.set(category, (categoryMap.get(category) || 0) + 1);
    });

    return Array.from(categoryMap.entries()).map(([name, value], index) => ({
      name,
      value,
      color: COLORS[index % COLORS.length],
    }));
  };

  const handleRefresh = () => {
    setLoading(true);
    Promise.all([
      refetchRestaurants(),
      refetchUsers(),
      refetchOrders(),
      refetchReviews(),
    ]).then(() => {
      setLastUpdated(new Date());
    });
  };

  const formatCurrency = (amount: number) => {
    return `${amount.toLocaleString('fa-IR')} ؋`;
  };

  const formatGrowth = (growth: number) => {
    const isPositive = growth >= 0;
    return (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
        {isPositive ? (
          <TrendingUpIcon fontSize="small" color="success" />
        ) : (
          <TrendingDownIcon fontSize="small" color="error" />
        )}
        <Typography
          variant="body2"
          color={isPositive ? 'success.main' : 'error.main'}
          fontWeight="medium"
        >
          {Math.abs(growth).toFixed(1)}%
        </Typography>
      </Box>
    );
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <Typography>در حال بارگذاری داشبورد...</Typography>
      </Box>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Stack
        direction="row"
        justifyContent="space-between"
        alignItems="center"
        mb={3}
      >
        <Typography variant="h4" component="h1">
          داشبورد تحلیلی
        </Typography>
        <Stack direction="row" spacing={2} alignItems="center">
          <Typography variant="body2" color="text.secondary">
            آخرین بروزرسانی: {lastUpdated.toLocaleTimeString('fa-IR')}
          </Typography>
          <Tooltip title="بروزرسانی">
            <IconButton onClick={handleRefresh} disabled={loading}>
              <RefreshIcon />
            </IconButton>
          </Tooltip>
          <FormControl size="small" sx={{ minWidth: 150 }}>
            <InputLabel>بازه زمانی</InputLabel>
            <Select
              value={dateRange}
              label="بازه زمانی"
              onChange={e => setDateRange(e.target.value)}
            >
              <MenuItem value="24hours">24 ساعت گذشته</MenuItem>
              <MenuItem value="7days">7 روز گذشته</MenuItem>
              <MenuItem value="30days">30 روز گذشته</MenuItem>
              <MenuItem value="90days">90 روز گذشته</MenuItem>
            </Select>
          </FormControl>
        </Stack>
      </Stack>

      {/* Key Metrics */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Stack direction="row" alignItems="center" spacing={2}>
                <Box
                  sx={{
                    p: 1,
                    backgroundColor: 'primary.light',
                    borderRadius: 1,
                  }}
                >
                  <RestaurantIcon sx={{ color: 'primary.main' }} />
                </Box>
                <Box sx={{ flex: 1 }}>
                  <Typography variant="h4" fontWeight="bold">
                    {stats?.overview.totalRestaurants.toLocaleString('fa-IR')}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    رستوران‌ها
                  </Typography>
                  {formatGrowth(stats?.growth.restaurants || 0)}
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Stack direction="row" alignItems="center" spacing={2}>
                <Box
                  sx={{
                    p: 1,
                    backgroundColor: 'success.light',
                    borderRadius: 1,
                  }}
                >
                  <PeopleIcon sx={{ color: 'success.main' }} />
                </Box>
                <Box sx={{ flex: 1 }}>
                  <Typography variant="h4" fontWeight="bold">
                    {stats?.overview.totalUsers.toLocaleString('fa-IR')}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    کاربران
                  </Typography>
                  {formatGrowth(stats?.growth.users || 0)}
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Stack direction="row" alignItems="center" spacing={2}>
                <Box
                  sx={{ p: 1, backgroundColor: 'info.light', borderRadius: 1 }}
                >
                  <OrderIcon sx={{ color: 'info.main' }} />
                </Box>
                <Box sx={{ flex: 1 }}>
                  <Typography variant="h4" fontWeight="bold">
                    {stats?.overview.totalOrders.toLocaleString('fa-IR')}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    سفارش‌ها
                  </Typography>
                  {formatGrowth(stats?.growth.orders || 0)}
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Stack direction="row" alignItems="center" spacing={2}>
                <Box
                  sx={{
                    p: 1,
                    backgroundColor: 'warning.light',
                    borderRadius: 1,
                  }}
                >
                  <RevenueIcon sx={{ color: 'warning.main' }} />
                </Box>
                <Box sx={{ flex: 1 }}>
                  <Typography variant="h4" fontWeight="bold">
                    {formatCurrency(stats?.overview.totalRevenue || 0)}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    درآمد کل
                  </Typography>
                  {formatGrowth(stats?.growth.revenue || 0)}
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        {/* Revenue Chart */}
        <Grid item xs={12} lg={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                روند درآمد و سفارش‌ها
              </Typography>
              <Box sx={{ height: 300 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={stats?.revenueChart}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis yAxisId="left" />
                    <YAxis yAxisId="right" orientation="right" />
                    <RechartsTooltip />
                    <Legend />
                    <Bar
                      yAxisId="right"
                      dataKey="orders"
                      fill="#8884d8"
                      name="تعداد سفارش"
                    />
                    <Line
                      yAxisId="left"
                      type="monotone"
                      dataKey="revenue"
                      stroke="#82ca9d"
                      name="درآمد (؋)"
                      strokeWidth={2}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Order Status Distribution */}
        <Grid item xs={12} lg={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                وضعیت سفارش‌ها
              </Typography>
              <Stack spacing={2}>
                {Object.entries(stats?.orderStats || {}).map(
                  ([status, count]) => (
                    <Box key={status}>
                      <Stack
                        direction="row"
                        justifyContent="space-between"
                        mb={1}
                      >
                        <Typography variant="body2">
                          {status === 'pending'
                            ? 'در انتظار'
                            : status === 'confirmed'
                              ? 'تایید شده'
                              : status === 'preparing'
                                ? 'در حال آماده‌سازی'
                                : status === 'ready'
                                  ? 'آماده تحویل'
                                  : status === 'delivered'
                                    ? 'تحویل داده شده'
                                    : status === 'cancelled'
                                      ? 'لغو شده'
                                      : status}
                        </Typography>
                        <Typography variant="body2" fontWeight="bold">
                          {count}
                        </Typography>
                      </Stack>
                      <LinearProgress
                        variant="determinate"
                        value={
                          stats?.overview.totalOrders
                            ? (count / stats.overview.totalOrders) * 100
                            : 0
                        }
                        sx={{ height: 6, borderRadius: 3 }}
                      />
                    </Box>
                  )
                )}
              </Stack>
            </CardContent>
          </Card>
        </Grid>

        {/* Top Restaurants */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                برترین رستوران‌ها
              </Typography>
              <Stack spacing={2}>
                {stats?.topRestaurants.map((restaurant, index) => (
                  <Paper key={restaurant.id} sx={{ p: 2 }}>
                    <Stack direction="row" alignItems="center" spacing={2}>
                      <Chip label={index + 1} color="primary" size="small" />
                      <Avatar>
                        <RestaurantIcon />
                      </Avatar>
                      <Box sx={{ flex: 1 }}>
                        <Typography variant="subtitle2" fontWeight="medium">
                          {restaurant.name}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {restaurant.city}
                        </Typography>
                      </Box>
                      <Box sx={{ textAlign: 'right' }}>
                        <Typography variant="body2" fontWeight="bold">
                          {formatCurrency(restaurant.revenue)}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {restaurant.orders} سفارش
                        </Typography>
                      </Box>
                    </Stack>
                  </Paper>
                ))}
              </Stack>
            </CardContent>
          </Card>
        </Grid>

        {/* User Activity */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                فعالیت کاربران (24 ساعت)
              </Typography>
              <Box sx={{ height: 250 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart data={stats?.userActivity}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="hour" />
                    <YAxis />
                    <RechartsTooltip />
                    <Area
                      type="monotone"
                      dataKey="orders"
                      stackId="1"
                      stroke="#8884d8"
                      fill="#8884d8"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Category Distribution */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                توزیع دسته‌بندی رستوران‌ها
              </Typography>
              <Box sx={{ height: 250 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={stats?.categoryDistribution}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) =>
                        `${name} ${((percent || 0) * 100).toFixed(0)}%`
                      }
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {stats?.categoryDistribution.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <RechartsTooltip />
                  </PieChart>
                </ResponsiveContainer>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Additional Metrics */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                آمار تکمیلی
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="h5" fontWeight="bold" color="primary">
                      {formatCurrency(stats?.overview.avgOrderValue || 0)}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      میانگین ارزش سفارش
                    </Typography>
                  </Paper>
                </Grid>
                <Grid item xs={6}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Stack
                      direction="row"
                      alignItems="center"
                      justifyContent="center"
                      spacing={1}
                    >
                      <Typography
                        variant="h5"
                        fontWeight="bold"
                        color="warning.main"
                      >
                        {(stats?.overview.avgRating || 0).toFixed(1)}
                      </Typography>
                      <ReviewIcon color="warning" />
                    </Stack>
                    <Typography variant="body2" color="text.secondary">
                      میانگین امتیاز
                    </Typography>
                  </Paper>
                </Grid>
                <Grid item xs={6}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Typography
                      variant="h5"
                      fontWeight="bold"
                      color="success.main"
                    >
                      {stats?.overview.activeRestaurants || 0}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      رستوران‌های فعال
                    </Typography>
                  </Paper>
                </Grid>
                <Grid item xs={6}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Typography
                      variant="h5"
                      fontWeight="bold"
                      color="info.main"
                    >
                      {stats?.overview.totalReviews || 0}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      کل نظرات
                    </Typography>
                  </Paper>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
}
