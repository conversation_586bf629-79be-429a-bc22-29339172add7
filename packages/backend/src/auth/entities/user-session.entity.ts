import {
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';

@Entity()
export class UserSession {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => User, (user) => user.sessions)
  user: User;

  @Column()
  token: string;

  @Column({ nullable: true })
  refresh_token: string;

  @Column({ nullable: true })
  device_info: string;

  @Column({ nullable: true })
  ip_address: string;

  @Column()
  expires_at: Date;

  @Column({ nullable: true })
  last_activity: Date;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
}
