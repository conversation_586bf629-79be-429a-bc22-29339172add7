'use client';

import { Refine } from '@refinedev/core';
import {
  RefineSnackbarProvider,
  useNotificationProvider,
} from '@refinedev/mui';
import { CssBaseline, GlobalStyles } from '@mui/material';
import routerProvider from '@refinedev/nextjs-router/app';
import { usePathname } from 'next/navigation';

import { dataProvider } from '../providers/dataProvider';
import { authProvider } from '../providers/authProvider';
import { Layout } from '../components/layout';

// Inner component that uses the notification provider
const RefineAppInner: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const pathname = usePathname();
  const notificationProvider = useNotificationProvider();

  // Routes that should not have the admin layout
  const authRoutes = ['/login', '/register', '/auth/otp-verification'];
  const shouldUseLayout = !authRoutes.includes(pathname);

  return (
    <Refine
      dataProvider={dataProvider}
      authProvider={authProvider}
      notificationProvider={notificationProvider}
      routerProvider={routerProvider}
      resources={[
        {
          name: 'dashboard',
          list: '/dashboard',
          meta: {
            label: 'داشبورد',
            icon: 'DashboardIcon',
          },
        },
        {
          name: 'analytics',
          list: '/analytics/dashboard',
          meta: {
            label: 'تحلیل و آمار',
            icon: 'AnalyticsIcon',
          },
        },
        {
          name: 'restaurants',
          list: '/restaurants/list',
          create: '/restaurants/create',
          edit: '/restaurants/edit/:id',
          show: '/restaurants/show/:id',
          meta: {
            canDelete: true,
            label: 'رستوران‌ها',
            icon: 'RestaurantIcon',
          },
        },
        {
          name: 'users',
          list: '/users/list',
          create: '/users/create',
          edit: '/users/edit/:id',
          show: '/users/show/:id',
          meta: {
            canDelete: true,
            label: 'کاربران',
            icon: 'PeopleIcon',
          },
        },
        {
          name: 'orders',
          list: '/orders/list',
          show: '/orders/show/:id',
          meta: {
            canDelete: false,
            label: 'سفارش‌ها',
            icon: 'OrderIcon',
          },
        },
        {
          name: 'reviews',
          list: '/reviews/list',
          show: '/reviews/show/:id',
          meta: {
            canDelete: true,
            label: 'مدیریت نظرات',
            icon: 'StarIcon',
          },
        },
        {
          name: 'notifications',
          list: '/notifications/list',
          meta: {
            canDelete: true,
            label: 'اعلان‌ها',
            icon: 'NotificationsIcon',
          },
        },
        {
          name: 'notifications-analytics',
          list: '/notifications/analytics',
          meta: {
            label: 'آمار اعلان‌ها',
            icon: 'AssessmentIcon',
            parent: 'notifications',
          },
        },
        {
          name: 'menu-items',
          list: '/menu-items/list',
          create: '/menu-items/create',
          edit: '/menu-items/edit/:id',
          show: '/menu-items/show/:id',
          meta: {
            canDelete: true,
            label: 'آیتم‌های منو',
            icon: 'MenuBookIcon',
          },
        },
        // {
        //   name: "reservations",
        //   list: "/reservations/list",
        //   show: "/reservations/show/:id",
        //   meta: {
        //     canDelete: true,
        //     label: "رزرو",
        //     icon: "EventIcon",
        //   },
        // }, // DISABLED: Reservation system removed
        {
          name: 'categories',
          list: '/categories/list',
          create: '/categories/create',
          edit: '/categories/edit/:id',
          meta: {
            canDelete: true,
            label: 'دسته‌بندی‌ها',
            icon: 'CategoryIcon',
          },
        },
        {
          name: 'credits',
          list: '/credits/list',
          meta: {
            label: 'اعتبار کاربران',
            icon: 'AccountBalanceWalletIcon',
          },
        },
        {
          name: 'credit-transactions',
          list: '/credits/transactions',
          meta: {
            label: 'تراکنش‌های اعتبار',
            icon: 'SwapHorizIcon',
            parent: 'credits',
          },
        },
      ]}
      options={{
        syncWithLocation: true,
        warnWhenUnsavedChanges: true,
        useNewQueryKeys: true,
        projectId: 'azkonja-admin',
      }}
    >
      {shouldUseLayout ? <Layout>{children}</Layout> : children}
    </Refine>
  );
};

// Main component that provides the snackbar context
const RefineApp: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <>
      <CssBaseline />
      <GlobalStyles styles={{ html: { WebkitFontSmoothing: 'auto' } }} />
      <RefineSnackbarProvider>
        <RefineAppInner>{children}</RefineAppInner>
      </RefineSnackbarProvider>
    </>
  );
};

export default RefineApp;
