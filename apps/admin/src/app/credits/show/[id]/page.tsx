'use client';

import React, { useState } from 'react';
import { useOne, useCustomMutation, useGo } from '@refinedev/core';
import { useParams } from 'next/navigation';
import {
  Box,
  Typography,
  Stack,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
} from '@mui/material';

export default function CreditShowPage() {
  const { id } = useParams();
  const go = useGo();
  const { data, isLoading } = useOne({
    resource: 'credits',
    id: Array.isArray(id) ? id[0] : id,
  });
  const credit = data?.data;

  // Dialog state
  const [openAdd, setOpenAdd] = useState(false);
  const [openDeduct, setOpenDeduct] = useState(false);
  const [amount, setAmount] = useState('');
  const [description, setDescription] = useState('');

  // Mutations
  const { mutate: addCredit, isPending: isAdding } = useCustomMutation();
  const { mutate: deductCredit, isPending: isDeducting } = useCustomMutation();

  const handleAddCredit = async () => {
    if (!credit) return;
    try {
      await addCredit({
        url: '/credits/add',
        method: 'post',
        values: {
          user_id: credit.user.id,
          amount: Number(amount),
          description,
        },
        successNotification: () => ({
          message: 'Credit added successfully',
          type: 'success',
        }),
        errorNotification: () => ({
          message: 'Failed to add credit',
          type: 'error',
        }),
      });
      setOpenAdd(false);
      setAmount('');
      setDescription('');
    } catch (error) {
      // Error is handled by errorNotification
    }
  };

  const handleDeductCredit = async () => {
    if (!credit) return;
    try {
      await deductCredit({
        url: `/credits/deduct/${credit.user.id}`,
        method: 'post',
        values: {
          amount: Number(amount),
          description,
        },
        successNotification: () => ({
          message: 'Credit deducted successfully',
          type: 'success',
        }),
        errorNotification: () => ({
          message: 'Failed to deduct credit',
          type: 'error',
        }),
      });
      setOpenDeduct(false);
      setAmount('');
      setDescription('');
    } catch (error) {
      // Error is handled by errorNotification
    }
  };

  if (isLoading) return <div>Loading...</div>;
  if (!credit) return <div>Credit not found</div>;

  return (
    <Box p={3}>
      <Typography variant="h5" mb={2}>
        User Credit Details
      </Typography>
      <Stack spacing={2}>
        <Typography>User ID: {credit.user.id}</Typography>
        <Typography>User Name: {credit.user.name}</Typography>
        <Typography>Balance: {credit.balance}</Typography>
        {credit.lifetime_earned !== undefined && (
          <Typography>Lifetime Earned: {credit.lifetime_earned}</Typography>
        )}
        {credit.created_at && (
          <Typography>
            Created At: {new Date(credit.created_at).toLocaleString()}
          </Typography>
        )}
        {credit.updated_at && (
          <Typography>
            Updated At: {new Date(credit.updated_at).toLocaleString()}
          </Typography>
        )}
        <Stack direction="row" spacing={2} mt={2}>
          <Button
            variant="contained"
            color="primary"
            onClick={() => setOpenAdd(true)}
          >
            Add Credit
          </Button>
          <Button
            variant="contained"
            color="secondary"
            onClick={() => setOpenDeduct(true)}
          >
            Deduct Credit
          </Button>
          <Button
            variant="outlined"
            onClick={() => go({ to: '/credits/list' })}
          >
            Back to List
          </Button>
        </Stack>
      </Stack>
      {/* Add Credit Dialog */}
      <Dialog open={openAdd} onClose={() => setOpenAdd(false)}>
        <DialogTitle>Add Credit</DialogTitle>
        <DialogContent>
          <TextField
            label="Amount"
            type="number"
            value={amount}
            onChange={e => setAmount(e.target.value)}
            fullWidth
            margin="normal"
          />
          <TextField
            label="Description"
            value={description}
            onChange={e => setDescription(e.target.value)}
            fullWidth
            margin="normal"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenAdd(false)}>Cancel</Button>
          <Button onClick={handleAddCredit} disabled={isAdding}>
            Add
          </Button>
        </DialogActions>
      </Dialog>
      {/* Deduct Credit Dialog */}
      <Dialog open={openDeduct} onClose={() => setOpenDeduct(false)}>
        <DialogTitle>Deduct Credit</DialogTitle>
        <DialogContent>
          <TextField
            label="Amount"
            type="number"
            value={amount}
            onChange={e => setAmount(e.target.value)}
            fullWidth
            margin="normal"
          />
          <TextField
            label="Description"
            value={description}
            onChange={e => setDescription(e.target.value)}
            fullWidth
            margin="normal"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDeduct(false)}>Cancel</Button>
          <Button onClick={handleDeductCredit} disabled={isDeducting}>
            Deduct
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}
