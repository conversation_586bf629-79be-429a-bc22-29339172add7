{"$schema": "https://turbo.build/schema.json", "ui": "tui", "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".env*", "next.config.*", "tailwind.config.*", "postcss.config.*", "nest-cli.json"], "outputs": ["dist/**", ".next/**", "!.next/cache/**", "build/**"], "env": ["NODE_ENV", "NEXT_PUBLIC_*", "DATABASE_*", "JWT_*", "REDIS_*", "CLOUDINARY_*"]}, "dev": {"cache": false, "persistent": true, "dependsOn": ["^build"], "env": ["NODE_ENV", "NEXT_PUBLIC_*", "DATABASE_*", "JWT_*", "REDIS_*", "CLOUDINARY_*"]}, "start": {"cache": false, "persistent": true, "dependsOn": ["build"]}, "start:dev": {"cache": false, "persistent": true, "dependsOn": ["^build"], "env": ["NODE_ENV", "DATABASE_*", "JWT_*", "REDIS_*", "CLOUDINARY_*"]}, "lint": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".eslintrc*", "eslint.config.*"], "outputs": []}, "type-check": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", "tsconfig*.json"], "outputs": []}, "test": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", "jest.config.*", "vitest.config.*"], "outputs": ["coverage/**"]}, "clean": {"cache": false, "inputs": [], "outputs": []}, "lint:fix": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".eslintrc*", "eslint.config.*", ".es<PERSON><PERSON><PERSON>"], "outputs": [], "cache": false}, "format": {"inputs": ["**/*.{ts,tsx,js,jsx,json,md}", ".prettierrc*", ".prettieri<PERSON>re"], "outputs": [], "cache": false}, "format:check": {"inputs": ["**/*.{ts,tsx,js,jsx,json,md}", ".prettierrc*", ".prettieri<PERSON>re"], "outputs": []}}, "globalDependencies": ["package.json", "pnpm-workspace.yaml", "turbo.json", ".env*", ".prettierrc*", ".eslintrc*"], "globalEnv": ["NODE_ENV", "NEXT_PUBLIC_*", "DATABASE_*", "JWT_*", "REDIS_*", "CLOUDINARY_*", "PORT"]}