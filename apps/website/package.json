{"name": "@azkuja/website", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev --turbo -p 3000", "dev:legacy": "next dev -p 3000", "build": "next build", "start": "next start -p 3000", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "echo \"No tests configured for website\"", "clean": "rm -rf .next dist"}, "dependencies": {"@heroicons/react": "^2.2.0", "axios": "^1.11.0", "clsx": "^2.1.1", "next": "^15.4.4", "next-themes": "^0.4.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-dropzone": "^14.3.8", "react-icons": "^5.5.0", "@tanstack/react-query": "^5.62.8", "socket.io-client": "^4.8.1"}, "devDependencies": {"@types/node": "^24.1.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "autoprefixer": "^10.4.21", "eslint": "^9.32.0", "eslint-config-next": "^15.4.4", "postcss": "^8.5.6", "tailwindcss": "^3.4.0", "typescript": "^5.8.3"}}