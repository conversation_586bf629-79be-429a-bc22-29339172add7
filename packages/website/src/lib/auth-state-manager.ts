/**
 * Global Authentication State Manager
 * 
 * Prevents race conditions during rapid page refreshes by coordinating
 * authentication checks across multiple AuthContext instances.
 */

interface AuthCheckResult {
  user: any | null;
  success: boolean;
  error?: string;
}

class AuthStateManager {
  private isChecking: boolean = false;
  private checkPromise: Promise<AuthCheckResult> | null = null;
  private lastCheckTime: number = 0;
  private readonly CHECK_COOLDOWN = 1000; // 1 second cooldown between checks

  /**
   * Performs authentication check with global coordination
   */
  async checkAuthStatus(apiService: any): Promise<AuthCheckResult> {
    const now = Date.now();
    
    // If a check is already in progress, wait for it
    if (this.isChecking && this.checkPromise) {
      console.log('🔒 AuthStateManager: Authentication check already in progress, waiting...');
      return this.checkPromise;
    }

    // If we just checked recently, return cached result
    if (now - this.lastCheckTime < this.CHECK_COOLDOWN) {
      console.log('🔒 AuthStateManager: Recent check found, skipping...');
      const token = this.getStoredToken();
      const user = this.getStoredUser();
      return {
        user: token && user ? user : null,
        success: !!(token && user)
      };
    }

    // Start new authentication check
    console.log('🔒 AuthStateManager: Starting new authentication check...');
    this.isChecking = true;
    this.checkPromise = this.performAuthCheck(apiService);

    try {
      const result = await this.checkPromise;
      this.lastCheckTime = now;
      return result;
    } finally {
      // Clear the lock
      this.isChecking = false;
      this.checkPromise = null;
    }
  }

  private async performAuthCheck(apiService: any): Promise<AuthCheckResult> {
    try {
      const token = this.getStoredToken();
      const refreshToken = this.getStoredRefreshToken();
      
      console.log('🔒 AuthStateManager: Found tokens:', { 
        hasToken: !!token, 
        hasRefreshToken: !!refreshToken 
      });

      if (!token && !refreshToken) {
        console.log('🔒 AuthStateManager: No tokens found');
        return { user: null, success: false };
      }

      // Try to get current user - this will handle token refresh automatically
      console.log('🔒 AuthStateManager: Attempting to get current user from API...');
      const userData = await apiService.getCurrentUserFromAPI();
      
      if (userData) {
        console.log('✅ AuthStateManager: User data retrieved successfully:', userData.name);
        // Update stored user data
        this.setStoredUser(userData);
        return { user: userData, success: true };
      } else {
        console.log('❌ AuthStateManager: No user data received');
        return { user: null, success: false, error: 'No user data received' };
      }
    } catch (error) {
      console.error('❌ AuthStateManager: Authentication check failed:', error);
      return { 
        user: null, 
        success: false, 
        error: error instanceof Error ? error.message : 'Authentication check failed' 
      };
    }
  }

  /**
   * Safely clears authentication tokens only if no other check is in progress
   */
  safeClearTokens(): boolean {
    if (this.isChecking) {
      console.log('🔒 AuthStateManager: Skipping token clear - authentication check in progress');
      return false;
    }

    console.log('🔒 AuthStateManager: Safely clearing authentication tokens');
    this.clearStoredTokens();
    this.lastCheckTime = 0; // Reset check time
    return true;
  }

  /**
   * Forces token clear (use only when absolutely necessary)
   */
  forceClearTokens(): void {
    console.log('🔒 AuthStateManager: Force clearing authentication tokens');
    this.clearStoredTokens();
    this.lastCheckTime = 0;
    this.isChecking = false;
    this.checkPromise = null;
  }

  // Helper methods for localStorage operations
  private getStoredToken(): string | null {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem('auth_token');
  }

  private getStoredRefreshToken(): string | null {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem('auth_refresh_token');
  }

  private getStoredUser(): any | null {
    if (typeof window === 'undefined') return null;
    const userStr = localStorage.getItem('auth_user');
    try {
      return userStr ? JSON.parse(userStr) : null;
    } catch {
      return null;
    }
  }

  private setStoredUser(user: any): void {
    if (typeof window === 'undefined') return;
    localStorage.setItem('auth_user', JSON.stringify(user));
  }

  private clearStoredTokens(): void {
    if (typeof window === 'undefined') return;
    localStorage.removeItem('auth_token');
    localStorage.removeItem('auth_refresh_token');
    localStorage.removeItem('auth_user');
  }

  /**
   * Check if authentication check is currently in progress
   */
  isAuthCheckInProgress(): boolean {
    return this.isChecking;
  }
}

// Export singleton instance
export const authStateManager = new AuthStateManager();
