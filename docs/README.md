# Azkuja Restaurant Directory

A comprehensive restaurant directory application with mobile app, customer website, admin panel, and backend API. This platform allows customers to discover restaurants, place orders, make reservations, and manage their dining experience, while providing restaurant owners and administrators with powerful management tools.

## 🏗️ Architecture Overview

This is a monorepo containing the following packages:

- **`packages/backend`**: NestJS backend API with PostgreSQL and Redis
- **`packages/website`**: Next.js customer-facing website (port 3000)
- **`packages/admin`**: Next.js admin panel for restaurant management (port 5000)
- **`packages/mobile`**: React Native/Expo mobile application
- **`packages/shared`**: Shared TypeScript types, utilities, and configurations

## 📋 Prerequisites

Before running the application, ensure you have the following installed:

### Required Software
- **Node.js** >= 18.0.0 ([Download](https://nodejs.org/))
- **Yarn** >= 1.22.0 ([Install Guide](https://yarnpkg.com/getting-started/install))
- **Docker** and **Docker Compose** ([Install Guide](https://docs.docker.com/get-docker/))
- **Git** ([Download](https://git-scm.com/downloads))

### For Mobile Development (Optional)
- **Expo CLI**: `npm install -g @expo/cli`
- **Android Studio** (for Android development)
- **Xcode** (for iOS development, macOS only)

### Verify Installation
```bash
node --version    # Should be >= 18.0.0
yarn --version    # Should be >= 1.22.0
docker --version  # Should be installed
git --version     # Should be installed
```

## 🚀 Quick Start Guide

### Step 1: Clone the Repository
```bash
git clone https://github.com/hrkhavarie/Azkuja.git
cd Azkuja
```

### Step 2: Install Dependencies
```bash
# Install all dependencies for all packages
yarn install
```

### Step 3: Set Up Environment Variables

Create environment files for each package:

#### Backend Environment (.env)
```bash
# Create backend environment file
cat > packages/backend/.env << EOF
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=postgres
DB_NAME=azkuja

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379

# Application Configuration
NODE_ENV=development
PORT=7000
API_PREFIX=api

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_DEST=./uploads

# Email Configuration (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# AWS S3 Configuration (Optional)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=your-bucket-name
EOF
```

#### Admin Panel Environment (.env.local)
```bash
# Create admin environment file
cat > packages/admin/.env.local << EOF
# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:7000/api

# Application Configuration
NODE_ENV=development
NEXT_PUBLIC_APP_NAME=Azkuja Admin

# Authentication
NEXTAUTH_URL=http://localhost:5000
NEXTAUTH_SECRET=your-nextauth-secret-change-this
EOF
```

#### Website Environment (.env.local)
```bash
# Create website environment file
cat > packages/website/.env.local << EOF
# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:7000/api
NEXT_PUBLIC_WS_URL=ws://localhost:7000

# Google Maps API (Optional)
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your-google-maps-api-key

# Application Configuration
NODE_ENV=development
NEXT_PUBLIC_APP_NAME=Azkuja
EOF
```

#### Mobile Environment (.env)
```bash
# Create mobile environment file
cat > packages/mobile/.env << EOF
# API Configuration
EXPO_PUBLIC_API_URL=http://localhost:7000/api

# App Configuration
EXPO_PUBLIC_APP_NAME=Azkuja
EXPO_PUBLIC_APP_VERSION=1.0.0
EOF
```

### Step 4: Start Database Services
```bash
# Start PostgreSQL and Redis using Docker
docker-compose up -d

# Verify services are running
docker-compose ps
```

### Step 5: Set Up Database
```bash
# The database will be automatically created when the backend starts
# Tables will be created automatically via TypeORM synchronization
```

### Step 6: Start the Applications

Open **4 separate terminal windows/tabs** and run the following commands:

#### Terminal 1: Start Backend API
```bash
cd Azkuja
yarn dev:backend
```
The backend will be available at: http://localhost:7000/api
API Documentation: http://localhost:7000/api/docs

#### Terminal 2: Start Admin Panel
```bash
cd Azkuja
yarn dev:admin
```
Admin panel will be available at: http://localhost:5000

#### Terminal 3: Start Customer Website
```bash
cd Azkuja
yarn dev:website
```
Customer website will be available at: http://localhost:3000

#### Terminal 4: Start Mobile App (Optional)
```bash
cd Azkuja
yarn dev:mobile
```
This will start the Expo development server. Use the Expo Go app on your phone or an emulator to run the mobile app.

## 🔧 Development Workflow

### Available Scripts

#### Root Level Scripts
```bash
# Start individual services
yarn dev:backend    # Start backend API
yarn dev:admin      # Start admin panel
yarn dev:website    # Start customer website
yarn dev:mobile     # Start mobile app

# Build for production
yarn build:backend  # Build backend
yarn build:admin    # Build admin panel
yarn build:website  # Build customer website

# Work with specific packages
yarn backend <command>  # Run command in backend package
yarn admin <command>    # Run command in admin package
yarn website <command>  # Run command in website package
yarn mobile <command>   # Run command in mobile package
```

#### Backend Specific Scripts
```bash
cd packages/backend

# Development
yarn start:dev      # Start with hot reload
yarn start:debug    # Start with debug mode

# Database
yarn seed           # Seed database with test data
yarn test:db        # Test database connection

# Testing
yarn test           # Run unit tests
yarn test:e2e       # Run end-to-end tests
yarn test:cov       # Run tests with coverage
```

### Database Management

#### Reset Database
```bash
# Stop services
docker-compose down

# Remove volumes (this will delete all data)
docker-compose down -v

# Start fresh
docker-compose up -d
yarn dev:backend
```

#### Seed Test Data
```bash
cd packages/backend
yarn seed
```

## 🌐 Application URLs

Once all services are running:

- **Backend API**: http://localhost:7000/api
- **API Documentation**: http://localhost:7000/api/docs
- **Customer Website**: http://localhost:3000
- **Admin Panel**: http://localhost:5000
- **Database**: localhost:5432 (PostgreSQL)
- **Redis**: localhost:6379

## 📱 Mobile Development

### Running on Physical Device
1. Install **Expo Go** app on your phone
2. Run `yarn dev:mobile`
3. Scan the QR code with Expo Go (Android) or Camera app (iOS)

### Running on Emulator
1. Set up Android Studio or Xcode
2. Start an emulator
3. Run `yarn dev:mobile`
4. Press `a` for Android or `i` for iOS

## 🔍 Troubleshooting

### Common Issues

#### Port Already in Use
```bash
# Find and kill process using the port
# For Windows
netstat -ano | findstr :7000
taskkill /PID <PID> /F

# For macOS/Linux
lsof -ti:7000 | xargs kill -9
```

#### Database Connection Issues
```bash
# Check if Docker services are running
docker-compose ps

# View logs
docker-compose logs postgres
docker-compose logs redis

# Restart services
docker-compose restart
```

#### Node Modules Issues
```bash
# Clean install
rm -rf node_modules
rm -rf packages/*/node_modules
yarn install
```

#### Environment Variables Not Loading
- Ensure `.env` files are in the correct directories
- Restart the development servers after changing environment variables
- Check that environment variable names match exactly (case-sensitive)

#### Mobile App Not Connecting to API
- Ensure your phone and computer are on the same network
- Replace `localhost` with your computer's IP address in mobile `.env` file:
```bash
# Find your IP address
# Windows: ipconfig
# macOS/Linux: ifconfig

# Update mobile .env
EXPO_PUBLIC_API_URL=http://*************:7000/api
```

### Logs and Debugging

#### View Application Logs
```bash
# Backend logs
cd packages/backend
tail -f backend.log

# Docker logs
docker-compose logs -f postgres
docker-compose logs -f redis
```

#### Enable Debug Mode
```bash
# Backend debug mode
cd packages/backend
yarn start:debug

# Next.js debug mode
DEBUG=* yarn dev:admin
DEBUG=* yarn dev:website
```

## 🧪 Testing

### Backend Testing
```bash
cd packages/backend

# Unit tests
yarn test

# E2E tests
yarn test:e2e

# Test coverage
yarn test:cov

# Test database connection
yarn test:db
```

### Frontend Testing
```bash
# Admin panel
cd packages/admin
yarn test

# Website
cd packages/website
yarn test
```

## 📦 Production Deployment

### Build for Production
```bash
# Build all packages
yarn build:backend
yarn build:admin
yarn build:website

# Or build individually
cd packages/backend && yarn build
cd packages/admin && yarn build
cd packages/website && yarn build
```

### Environment Variables for Production
- Change all secrets and passwords
- Use production database credentials
- Set `NODE_ENV=production`
- Configure proper CORS origins
- Set up SSL certificates

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit your changes: `git commit -m 'Add amazing feature'`
4. Push to the branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

### Git Workflow

- `main`: Production-ready code
- `develop`: Integration branch for features
- `feature/*`: New features
- `bugfix/*`: Bug fixes
- `release/*`: Release preparation

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

If you encounter any issues:

1. Check the [Troubleshooting](#-troubleshooting) section
2. Search existing [GitHub Issues](https://github.com/hrkhavarie/Azkuja/issues)
3. Create a new issue with detailed information about the problem

## 📚 Additional Resources

- [NestJS Documentation](https://nestjs.com/)
- [Next.js Documentation](https://nextjs.org/docs)
- [React Native Documentation](https://reactnative.dev/)
- [Expo Documentation](https://docs.expo.dev/)
- [TypeORM Documentation](https://typeorm.io/)
- [Docker Documentation](https://docs.docker.com/)

---

**Happy Coding! 🚀** 