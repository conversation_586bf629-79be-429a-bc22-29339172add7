'use client';

import React, { useState, useEffect } from 'react';
import { useList } from '@refinedev/core';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Stack,
  LinearProgress,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Alert,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  NotificationsActive as NotificationIcon,
  Send as SendIcon,
  Visibility as ViewIcon,
  TouchApp as ClickIcon,
  Schedule as ScheduleIcon,
  Group as GroupIcon,
  CheckCircle as SuccessIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
} from '@mui/icons-material';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

interface NotificationStats {
  totalSent: number;
  totalDelivered: number;
  totalRead: number;
  totalClicked: number;
  deliveryRate: number;
  readRate: number;
  clickRate: number;
  byType: Record<string, number>;
  byPriority: Record<string, number>;
  recentActivity: any[];
  topPerforming: any[];
  failedDeliveries: any[];
}

export default function NotificationsAnalytics() {
  const [dateRange, setDateRange] = useState('7days');
  const [stats, setStats] = useState<NotificationStats | null>(null);
  const [loading, setLoading] = useState(true);

  // Fetch notifications data
  const { data: notificationsData, isLoading: notificationsLoading } = useList({
    resource: 'notifications',
    pagination: { mode: 'off' },
    filters: [
      {
        field: 'created_at',
        operator: 'gte',
        value: getDateRangeStart(dateRange),
      },
    ],
  });

  // Fetch notification stats from API
  const { data: statsData, isLoading: statsLoading } = useList({
    resource: 'notifications/stats',
    pagination: { mode: 'off' },
    filters: [
      {
        field: 'period',
        operator: 'eq',
        value: dateRange,
      },
    ],
  });

  function getDateRangeStart(range: string): string {
    const now = new Date();
    switch (range) {
      case '24hours':
        return new Date(now.getTime() - 24 * 60 * 60 * 1000).toISOString();
      case '7days':
        return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString();
      case '30days':
        return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000).toISOString();
      case '90days':
        return new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000).toISOString();
      default:
        return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString();
    }
  }

  useEffect(() => {
    if (!notificationsLoading && !statsLoading) {
      calculateStats();
    }
  }, [notificationsData, statsData, notificationsLoading, statsLoading]);

  const calculateStats = () => {
    try {
      const notifications = notificationsData?.data || [];
      const apiStats = statsData?.data || {};

      // Calculate basic metrics
      const totalSent = notifications.length;
      const totalDelivered = notifications.reduce(
        (sum: number, n: any) => sum + (n.delivered_count || 0),
        0
      );
      const totalRead = notifications.reduce(
        (sum: number, n: any) => sum + (n.read_count || 0),
        0
      );
      const totalClicked = notifications.reduce(
        (sum: number, n: any) => sum + (n.click_count || 0),
        0
      );

      const deliveryRate =
        totalSent > 0 ? (totalDelivered / totalSent) * 100 : 0;
      const readRate =
        totalDelivered > 0 ? (totalRead / totalDelivered) * 100 : 0;
      const clickRate = totalRead > 0 ? (totalClicked / totalRead) * 100 : 0;

      // Group by type
      const byType: Record<string, number> = {};
      notifications.forEach((n: any) => {
        byType[n.type] = (byType[n.type] || 0) + 1;
      });

      // Group by priority
      const byPriority: Record<string, number> = {};
      notifications.forEach((n: any) => {
        byPriority[n.priority] = (byPriority[n.priority] || 0) + 1;
      });

      // Recent activity (last 10 notifications)
      const recentActivity = notifications
        .sort(
          (a: any, b: any) =>
            new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        )
        .slice(0, 10);

      // Top performing notifications
      const topPerforming = notifications
        .filter((n: any) => n.read_count > 0)
        .sort((a: any, b: any) => (b.read_count || 0) - (a.read_count || 0))
        .slice(0, 5);

      // Failed deliveries
      const failedDeliveries = notifications
        .filter((n: any) => n.failed_count > 0)
        .sort((a: any, b: any) => (b.failed_count || 0) - (a.failed_count || 0))
        .slice(0, 5);

      setStats({
        totalSent,
        totalDelivered,
        totalRead,
        totalClicked,
        deliveryRate,
        readRate,
        clickRate,
        byType,
        byPriority,
        recentActivity,
        topPerforming,
        failedDeliveries,
      });
    } catch (error) {
      console.error('Error calculating notification stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatNumber = (num: number) => {
    return num.toLocaleString('fa-IR');
  };

  const formatPercentage = (num: number) => {
    return `${num.toFixed(1)}%`;
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'info':
        return 'اطلاعات';
      case 'success':
        return 'موفقیت';
      case 'warning':
        return 'هشدار';
      case 'error':
        return 'خطا';
      case 'promotion':
        return 'تبلیغات';
      case 'order_update':
        return 'بروزرسانی سفارش';
      case 'system':
        return 'سیستم';
      default:
        return type;
    }
  };

  const getPriorityLabel = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'بالا';
      case 'normal':
        return 'معمولی';
      case 'low':
        return 'پایین';
      default:
        return priority;
    }
  };

  const getDateRangeLabel = (range: string) => {
    switch (range) {
      case '24hours':
        return '24 ساعت گذشته';
      case '7days':
        return '7 روز گذشته';
      case '30days':
        return '30 روز گذشته';
      case '90days':
        return '90 روز گذشته';
      default:
        return '7 روز گذشته';
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <Typography>در حال بارگذاری آمار...</Typography>
      </Box>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Stack
        direction="row"
        justifyContent="space-between"
        alignItems="center"
        mb={3}
      >
        <Typography variant="h4" component="h1">
          آمار و تحلیل اعلان‌ها
        </Typography>
        <FormControl size="small" sx={{ minWidth: 200 }}>
          <InputLabel>بازه زمانی</InputLabel>
          <Select
            value={dateRange}
            label="بازه زمانی"
            onChange={e => setDateRange(e.target.value)}
          >
            <MenuItem value="24hours">24 ساعت گذشته</MenuItem>
            <MenuItem value="7days">7 روز گذشته</MenuItem>
            <MenuItem value="30days">30 روز گذشته</MenuItem>
            <MenuItem value="90days">90 روز گذشته</MenuItem>
          </Select>
        </FormControl>
      </Stack>

      {/* Key Metrics */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Stack direction="row" alignItems="center" spacing={2}>
                <Box
                  sx={{
                    p: 1,
                    backgroundColor: 'primary.light',
                    borderRadius: 1,
                  }}
                >
                  <SendIcon sx={{ color: 'primary.main' }} />
                </Box>
                <Box>
                  <Typography variant="h4" fontWeight="bold">
                    {formatNumber(stats?.totalSent || 0)}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    اعلان ارسال شده
                  </Typography>
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Stack direction="row" alignItems="center" spacing={2}>
                <Box
                  sx={{
                    p: 1,
                    backgroundColor: 'success.light',
                    borderRadius: 1,
                  }}
                >
                  <SuccessIcon sx={{ color: 'success.main' }} />
                </Box>
                <Box>
                  <Typography variant="h4" fontWeight="bold">
                    {formatNumber(stats?.totalDelivered || 0)}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    تحویل موفق
                  </Typography>
                  <Typography variant="body2" color="success.main">
                    {formatPercentage(stats?.deliveryRate || 0)}
                  </Typography>
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Stack direction="row" alignItems="center" spacing={2}>
                <Box
                  sx={{ p: 1, backgroundColor: 'info.light', borderRadius: 1 }}
                >
                  <ViewIcon sx={{ color: 'info.main' }} />
                </Box>
                <Box>
                  <Typography variant="h4" fontWeight="bold">
                    {formatNumber(stats?.totalRead || 0)}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    خوانده شده
                  </Typography>
                  <Typography variant="body2" color="info.main">
                    {formatPercentage(stats?.readRate || 0)}
                  </Typography>
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Stack direction="row" alignItems="center" spacing={2}>
                <Box
                  sx={{
                    p: 1,
                    backgroundColor: 'warning.light',
                    borderRadius: 1,
                  }}
                >
                  <ClickIcon sx={{ color: 'warning.main' }} />
                </Box>
                <Box>
                  <Typography variant="h4" fontWeight="bold">
                    {formatNumber(stats?.totalClicked || 0)}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    کلیک شده
                  </Typography>
                  <Typography variant="body2" color="warning.main">
                    {formatPercentage(stats?.clickRate || 0)}
                  </Typography>
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        {/* Performance Overview */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                عملکرد کلی
              </Typography>
              <Stack spacing={2}>
                <Box>
                  <Stack direction="row" justifyContent="space-between" mb={1}>
                    <Typography variant="body2">نرخ تحویل</Typography>
                    <Typography variant="body2" fontWeight="bold">
                      {formatPercentage(stats?.deliveryRate || 0)}
                    </Typography>
                  </Stack>
                  <LinearProgress
                    variant="determinate"
                    value={stats?.deliveryRate || 0}
                    sx={{ height: 8, borderRadius: 4 }}
                  />
                </Box>

                <Box>
                  <Stack direction="row" justifyContent="space-between" mb={1}>
                    <Typography variant="body2">نرخ بازدید</Typography>
                    <Typography variant="body2" fontWeight="bold">
                      {formatPercentage(stats?.readRate || 0)}
                    </Typography>
                  </Stack>
                  <LinearProgress
                    variant="determinate"
                    value={stats?.readRate || 0}
                    color="info"
                    sx={{ height: 8, borderRadius: 4 }}
                  />
                </Box>

                <Box>
                  <Stack direction="row" justifyContent="space-between" mb={1}>
                    <Typography variant="body2">نرخ کلیک</Typography>
                    <Typography variant="body2" fontWeight="bold">
                      {formatPercentage(stats?.clickRate || 0)}
                    </Typography>
                  </Stack>
                  <LinearProgress
                    variant="determinate"
                    value={stats?.clickRate || 0}
                    color="warning"
                    sx={{ height: 8, borderRadius: 4 }}
                  />
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Grid>

        {/* Notification Types */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                توزیع بر اساس نوع
              </Typography>
              <Stack spacing={1}>
                {Object.entries(stats?.byType || {}).map(([type, count]) => (
                  <Stack
                    key={type}
                    direction="row"
                    justifyContent="space-between"
                    alignItems="center"
                  >
                    <Typography variant="body2">
                      {getTypeLabel(type)}
                    </Typography>
                    <Chip
                      label={formatNumber(count)}
                      size="small"
                      variant="outlined"
                    />
                  </Stack>
                ))}
              </Stack>
            </CardContent>
          </Card>
        </Grid>

        {/* Recent Activity */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                فعالیت اخیر
              </Typography>
              <List dense>
                {stats?.recentActivity.map(
                  (notification: any, index: number) => (
                    <ListItem key={index} divider>
                      <ListItemIcon>
                        <NotificationIcon color="primary" />
                      </ListItemIcon>
                      <ListItemText
                        primary={notification.title}
                        secondary={
                          <Stack
                            direction="row"
                            spacing={1}
                            alignItems="center"
                          >
                            <Typography variant="body2" color="text.secondary">
                              {new Date(
                                notification.created_at
                              ).toLocaleDateString('fa-IR')}
                            </Typography>
                            <Chip
                              label={getTypeLabel(notification.type)}
                              size="small"
                              variant="outlined"
                            />
                          </Stack>
                        }
                      />
                    </ListItem>
                  )
                )}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Top Performing */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                بهترین عملکرد
              </Typography>
              <List dense>
                {stats?.topPerforming.map(
                  (notification: any, index: number) => (
                    <ListItem key={index} divider>
                      <ListItemIcon>
                        <TrendingUpIcon color="success" />
                      </ListItemIcon>
                      <ListItemText
                        primary={notification.title}
                        secondary={
                          <Stack
                            direction="row"
                            spacing={1}
                            alignItems="center"
                          >
                            <Typography variant="body2" color="text.secondary">
                              {formatNumber(notification.read_count)} بازدید
                            </Typography>
                            <Typography variant="body2" color="success.main">
                              {formatPercentage(
                                (notification.read_count /
                                  notification.delivered_count) *
                                  100
                              )}
                            </Typography>
                          </Stack>
                        }
                      />
                    </ListItem>
                  )
                )}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Failed Deliveries */}
        {(stats?.failedDeliveries?.length ?? 0) > 0 && (
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  اعلان‌های با مشکل تحویل
                </Typography>
                <Alert severity="warning" sx={{ mb: 2 }}>
                  <Typography variant="body2">
                    اعلان‌های زیر در تحویل با مشکل مواجه شده‌اند. لطفاً بررسی
                    کنید.
                  </Typography>
                </Alert>
                <List dense>
                  {stats?.failedDeliveries?.map(
                    (notification: any, index: number) => (
                      <ListItem key={index} divider>
                        <ListItemIcon>
                          <ErrorIcon color="error" />
                        </ListItemIcon>
                        <ListItemText
                          primary={notification.title}
                          secondary={
                            <Stack
                              direction="row"
                              spacing={1}
                              alignItems="center"
                            >
                              <Typography
                                variant="body2"
                                color="text.secondary"
                              >
                                {formatNumber(notification.failed_count)} خطا
                              </Typography>
                              <Typography variant="body2" color="error.main">
                                {new Date(
                                  notification.created_at
                                ).toLocaleDateString('fa-IR')}
                              </Typography>
                            </Stack>
                          }
                        />
                      </ListItem>
                    )
                  )}
                </List>
              </CardContent>
            </Card>
          </Grid>
        )}
      </Grid>
    </Box>
  );
}
